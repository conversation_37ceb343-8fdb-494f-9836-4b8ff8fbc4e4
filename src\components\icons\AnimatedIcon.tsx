import React from 'react';

interface AnimatedIconProps {
  size?: number;
  className?: string;
  color?: string;
  isAnimation?: boolean;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-icon {
            animation: pulse 2s ease-in-out infinite;
          }
          
          .icon-path {
            animation: draw 3s ease-in-out infinite;
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
          }
          
          .icon-circle {
            animation: rotate 4s linear infinite;
            transform-origin: 50px 50px;
          }
          
          .icon-heart {
            animation: heartbeat 1.5s ease-in-out infinite;
            transform-origin: 50px 50px;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          
          @keyframes draw {
            0% { stroke-dashoffset: 200; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -200; }
          }
          
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
          }
        `}
      </style>

      {/* Animated Travel/Plane Icon */}
      <g className="icon-circle">
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeDasharray="5,5"
        />
      </g>

      <g className="icon-heart">
        <path
          d="M50 75 C30 55, 10 35, 30 25 C40 20, 50 30, 50 30 C50 30, 60 20, 70 25 C90 35, 70 55, 50 75 Z"
          fill={color}
          opacity="0.8"
        />
      </g>

      <path
        className="icon-path"
        d="M20 50 Q35 30, 50 50 T80 50"
        fill="none"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
      />

      {/* Animated dots */}
      <circle cx="25" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="50" cy="35" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0.5s"
        />
      </circle>
      <circle cx="75" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="1s"
        />
      </circle>
    </svg>
  );
};

// Animated Plane Icon Component
const AnimatedPlaneIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-plane-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-plane-icon {
            animation: float 3s ease-in-out infinite;
          }

          .plane-body {
            animation: fly 4s linear infinite;
            transform-origin: 50px 50px;
          }

          .plane-trail {
            animation: trail 2s ease-in-out infinite;
            stroke-dasharray: 10;
            stroke-dashoffset: 20;
          }

          .cloud {
            animation: drift 6s ease-in-out infinite;
          }

          .propeller {
            animation: spin 0.1s linear infinite;
            transform-origin: 35px 45px;
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
          }

          @keyframes fly {
            0% { transform: translateX(-10px); }
            50% { transform: translateX(5px); }
            100% { transform: translateX(-10px); }
          }

          @keyframes trail {
            0% { stroke-dashoffset: 20; opacity: 1; }
            100% { stroke-dashoffset: -20; opacity: 0.3; }
          }

          @keyframes drift {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(10px); }
          }

          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>

      {/* Clouds */}
      <g className="cloud">
        <ellipse cx="20" cy="25" rx="8" ry="4" fill={color} opacity="0.3" />
        <ellipse cx="75" cy="20" rx="6" ry="3" fill={color} opacity="0.2" />
      </g>

      {/* Plane body */}
      <g className="plane-body">
        {/* Main fuselage */}
        <ellipse cx="50" cy="45" rx="25" ry="4" fill={color} />

        {/* Wings */}
        <ellipse cx="45" cy="45" rx="15" ry="2" fill={color} />

        {/* Tail */}
        <polygon points="70,45 75,40 75,50" fill={color} />

        {/* Cockpit */}
        <circle cx="30" cy="45" r="3" fill={color} opacity="0.8" />
      </g>

      {/* Propeller */}
      <g className="propeller">
        <line x1="30" y1="45" x2="40" y2="45" stroke={color} strokeWidth="1" />
        <line x1="35" y1="40" x2="35" y2="50" stroke={color} strokeWidth="1" />
      </g>

      {/* Flight trail */}
      <path
        className="plane-trail"
        d="M75 45 Q85 40, 95 45 Q85 50, 75 45"
        fill="none"
        stroke={color}
        strokeWidth="2"
        opacity="0.6"
      />

      {/* Animated stars/sparkles */}
      <circle cx="15" cy="60" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="85" cy="65" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.7s"
        />
      </circle>
      <circle cx="60" cy="75" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="1.2s"
        />
      </circle>
    </svg>
  );
};

export default AnimatedIcon;
export { AnimatedPlaneIcon };

// Animated HeartIcon Component
const HeartIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  isAnimation = true,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${isAnimation ? 'animated-heart-icon' : ''} ${className}`}
    >
      {isAnimation && (
        <style>
          {`
            .animated-heart-icon {
              animation: heartPulse 1.5s ease-in-out infinite;
              transform-origin: center;
            }

            .heart-path {
              animation: heartFill 2s ease-in-out infinite;
            }

            @keyframes heartPulse {
              0%, 100% { transform: scale(1); }
              25% { transform: scale(1.1); }
              50% { transform: scale(1); }
              75% { transform: scale(1.05); }
            }

            @keyframes heartFill {
              0%, 100% { fill: ${color || '#707FF5'}; }
              50% { fill: #FF6B9D; }
            }
          `}
        </style>
      )}
      <path
        className={isAnimation ? "heart-path" : ""}
        d="M6.62444 14.7599L7.08876 14.1709L6.62444 14.7599ZM9.15633 3.58471L8.61597 4.10482C8.75735 4.25171 8.95245 4.33471 9.15633 4.33471C9.36021 4.33471 9.55531 4.25171 9.6967 4.10482L9.15633 3.58471ZM11.6882 14.7599L12.1525 15.3489L11.6882 14.7599ZM6.62444 14.7599L7.08876 14.1709C5.81829 13.1694 4.45104 12.2072 3.36503 10.9843C2.30474 9.7904 1.573 8.40726 1.573 6.6151H0.822998H0.072998C0.072998 8.87539 1.01602 10.5982 2.24347 11.9804C3.4452 13.3336 4.97867 14.4175 6.16013 15.3489L6.62444 14.7599ZM0.822998 6.6151H1.573C1.573 4.86948 2.55919 3.41209 3.89526 2.80137C5.18405 2.21226 6.93298 2.35626 8.61597 4.10482L9.15633 3.58471L9.6967 3.06461C7.62982 0.917201 5.21208 0.550169 3.27167 1.43714C1.37854 2.30249 0.072998 4.30828 0.072998 6.6151H0.822998ZM6.62444 14.7599L6.16013 15.3489C6.58584 15.6845 7.0508 16.0489 7.52395 16.3254C7.99686 16.6017 8.54927 16.8342 9.15633 16.8342V16.0842V15.3342C8.93006 15.3342 8.64914 15.2456 8.28074 15.0303C7.91258 14.8151 7.52826 14.5174 7.08876 14.1709L6.62444 14.7599ZM11.6882 14.7599L12.1525 15.3489C13.334 14.4175 14.8675 13.3336 16.0692 11.9804C17.2966 10.5982 18.2397 8.87539 18.2397 6.6151H17.4897H16.7397C16.7397 8.40726 16.0079 9.7904 14.9476 10.9843C13.8616 12.2072 12.4944 13.1694 11.2239 14.1709L11.6882 14.7599ZM17.4897 6.6151H18.2397C18.2397 4.30828 16.9341 2.30249 15.041 1.43714C13.1006 0.550169 10.6828 0.917201 8.61597 3.06461L9.15633 3.58471L9.6967 4.10482C11.3797 2.35626 13.1286 2.21226 14.4174 2.80137C15.7535 3.41209 16.7397 4.86948 16.7397 6.6151H17.4897ZM11.6882 14.7599L11.2239 14.1709C10.7844 14.5174 10.4001 14.8151 10.0319 15.0303C9.66352 15.2456 9.3826 15.3342 9.15633 15.3342V16.0842V16.8342C9.7634 16.8342 10.3158 16.6017 10.7887 16.3254C11.2619 16.0489 11.7268 15.6845 12.1525 15.3489L11.6882 14.7599Z"
        fill={color || "#707FF5"}
      />
    </svg>
  );
};

export { HeartIcon };

// Animated DownloadIcon Component
const DownloadIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  isAnimation = true,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${isAnimation ? 'animated-download-icon' : ''} ${className}`}
    >
      {isAnimation && (
        <style>
          {`
            .animated-download-icon {
              animation: downloadBounce 2s ease-in-out infinite;
            }

            .download-arrow {
              animation: downloadMove 1.5s ease-in-out infinite;
              transform-origin: center;
            }

            .download-container {
              animation: downloadFade 2s ease-in-out infinite;
            }

            @keyframes downloadBounce {
              0%, 100% { transform: translateY(0px); }
              50% { transform: translateY(-2px); }
            }

            @keyframes downloadMove {
              0%, 100% { transform: translateY(0px); }
              50% { transform: translateY(2px); }
            }

            @keyframes downloadFade {
              0%, 100% { opacity: 1; }
              50% { opacity: 0.7; }
            }
          `}
        </style>
      )}
      <path
        className={isAnimation ? "download-container" : ""}
        d="M2.57812 12.5C2.57812 14.857 2.57812 16.0355 3.31036 16.7678C4.04259 17.5 5.2211 17.5 7.57812 17.5H12.5781C14.9351 17.5 16.1137 17.5 16.8459 16.7678C17.5781 16.0355 17.5781 14.857 17.5781 12.5"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        className={isAnimation ? "download-arrow" : ""}
        d="M10.0782 2.50065V13.334M10.0782 13.334L13.4115 9.68815M10.0782 13.334L6.74487 9.68815"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export { DownloadIcon };

// Animated ShareIcon Component
const ShareIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-share-icon ${className}`}
    >
      <style>
        {`
          .animated-share-icon {
            animation: shareFloat 2.5s ease-in-out infinite;
          }

          .share-node {
            animation: shareNodePulse 2s ease-in-out infinite;
            transform-origin: center;
          }

          .share-connection {
            animation: shareConnectionFlow 1.8s ease-in-out infinite;
            stroke-dasharray: 10;
            stroke-dashoffset: 20;
          }

          .share-node-1 {
            animation-delay: 0s;
          }

          .share-node-2 {
            animation-delay: 0.3s;
          }

          .share-node-3 {
            animation-delay: 0.6s;
          }

          @keyframes shareFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-1px) rotate(1deg); }
          }

          @keyframes shareNodePulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
          }

          @keyframes shareConnectionFlow {
            0% { stroke-dashoffset: 20; opacity: 0.6; }
            50% { stroke-dashoffset: 0; opacity: 1; }
            100% { stroke-dashoffset: -20; opacity: 0.6; }
          }
        `}
      </style>
      <path
        className="share-node share-node-1"
        d="M7.49992 9.99935C7.49992 11.1499 6.56718 12.0827 5.41659 12.0827C4.26599 12.0827 3.33325 11.1499 3.33325 9.99935C3.33325 8.84876 4.26599 7.91602 5.41659 7.91602C6.56718 7.91602 7.49992 8.84876 7.49992 9.99935Z"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
      />
      <path
        className="share-connection"
        d="M11.6667 5.41602L7.5 8.33268"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        className="share-connection"
        d="M11.6667 14.584L7.5 11.6673"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        className="share-node share-node-2"
        d="M15.8334 15.4173C15.8334 16.5679 14.9007 17.5007 13.7501 17.5007C12.5995 17.5007 11.6667 16.5679 11.6667 15.4173C11.6667 14.2667 12.5995 13.334 13.7501 13.334C14.9007 13.334 15.8334 14.2667 15.8334 15.4173Z"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
      />
      <path
        className="share-node share-node-3"
        d="M15.8334 4.58333C15.8334 5.73393 14.9007 6.66667 13.7501 6.66667C12.5995 6.66667 11.6667 5.73393 11.6667 4.58333C11.6667 3.43274 12.5995 2.5 13.7501 2.5C14.9007 2.5 15.8334 3.43274 15.8334 4.58333Z"
        stroke={color || '#707FF5'}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export { ShareIcon };

// Animated BankNoteIcon Component
const BankNoteIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-banknote-icon ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      <style>
        {`
          .animated-banknote-icon {
            animation: banknoteSway 3s ease-in-out infinite;
          }

          .banknote-main {
            animation: banknoteFloat 2.5s ease-in-out infinite;
            transform-origin: center;
          }

          .banknote-shadow {
            animation: banknoteShadow 2.5s ease-in-out infinite;
          }

          .banknote-circle {
            animation: banknoteCirclePulse 2s ease-in-out infinite;
            transform-origin: center;
          }

          .banknote-lines {
            animation: banknoteLineShimmer 1.8s ease-in-out infinite;
          }

          @keyframes banknoteSway {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(1deg); }
            75% { transform: rotate(-1deg); }
          }

          @keyframes banknoteFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-1px); }
          }

          @keyframes banknoteShadow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 0.3; }
          }

          @keyframes banknoteCirclePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
          }

          @keyframes banknoteLineShimmer {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
        `}
      </style>
      <g clipPath="url(#clip0_497_8004)">
        <path
          className="banknote-main"
          d="M1.6792 9.66602C1.6792 7.30899 1.6792 6.13048 2.41143 5.39825C3.14367 4.66602 4.32218 4.66602 6.6792 4.66602H10.8459C13.2029 4.66602 14.3814 4.66602 15.1136 5.39825C15.8459 6.13048 15.8459 7.30899 15.8459 9.66602C15.8459 12.023 15.8459 13.2015 15.1136 13.9338C14.3814 14.666 13.2029 14.666 10.8459 14.666H6.6792C4.32218 14.666 3.14367 14.666 2.41143 13.9338C1.6792 13.2015 1.6792 12.023 1.6792 9.66602Z"
          stroke={color || '#384254'}
          strokeWidth="1.5"
        />
        <path
          className="banknote-shadow"
          d="M15.8459 7.23047C16.6585 7.3104 17.2054 7.49103 17.6137 7.89927C18.3459 8.6315 18.3459 9.81001 18.3459 12.167C18.3459 14.5241 18.3459 15.7026 17.6137 16.4348C16.8814 17.167 15.7029 17.167 13.3459 17.167H9.17925C6.82222 17.167 5.64371 17.167 4.91148 16.4348C4.50323 16.0266 4.3226 15.4796 4.24268 14.667"
          stroke={color || '#384254'}
          strokeWidth="1.5"
        />
        <path
          className="banknote-circle"
          d="M10.8459 9.66732C10.8459 10.8179 9.91313 11.7507 8.76253 11.7507C7.61194 11.7507 6.6792 10.8179 6.6792 9.66732C6.6792 8.51672 7.61194 7.58398 8.76253 7.58398C9.91313 7.58398 10.8459 8.51672 10.8459 9.66732Z"
          stroke={color || '#384254'}
          strokeWidth="1.5"
        />
        <path
          className="banknote-lines"
          d="M13.3457 11.334L13.3457 8.00065"
          stroke={color || '#384254'}
          strokeWidth="1.5"
          strokeLinecap="round"
        />
        <path
          className="banknote-lines"
          d="M4.1792 11.334L4.1792 8.00065"
          stroke={color || '#384254'}
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_497_8004">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.0124512 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export { BankNoteIcon };

// Animated CalendarIcon Component
const CalendarIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-calendar-icon ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      <style>
        {`
          .animated-calendar-icon {
            animation: calendarBob 2.8s ease-in-out infinite;
          }

          .calendar-date {
            animation: calendarDatePop 2s ease-in-out infinite;
            transform-origin: center;
          }

          .calendar-date-1 {
            animation-delay: 0s;
          }

          .calendar-date-2 {
            animation-delay: 0.2s;
          }

          .calendar-date-3 {
            animation-delay: 0.4s;
          }

          .calendar-date-4 {
            animation-delay: 0.6s;
          }

          .calendar-date-5 {
            animation-delay: 0.8s;
          }

          .calendar-date-6 {
            animation-delay: 1s;
          }

          .calendar-body {
            animation: calendarBodyFloat 3s ease-in-out infinite;
          }

          @keyframes calendarBob {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-1px); }
          }

          @keyframes calendarDatePop {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
          }

          @keyframes calendarBodyFloat {
            0%, 100% { transform: translateY(0px); }
            33% { transform: translateY(-0.5px); }
            66% { transform: translateY(0.5px); }
          }
        `}
      </style>
      <path
        className="calendar-date calendar-date-1"
        d="M13.9199 11.1673C14.3802 11.1673 14.7533 10.7942 14.7533 10.334C14.7533 9.87375 14.3802 9.50065 13.9199 9.50065C13.4597 9.50065 13.0866 9.87375 13.0866 10.334C13.0866 10.7942 13.4597 11.1673 13.9199 11.1673Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-date calendar-date-2"
        d="M13.9199 14.5007C14.3802 14.5007 14.7533 14.1276 14.7533 13.6673C14.7533 13.2071 14.3802 12.834 13.9199 12.834C13.4597 12.834 13.0866 13.2071 13.0866 13.6673C13.0866 14.1276 13.4597 14.5007 13.9199 14.5007Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-date calendar-date-3"
        d="M10.5866 10.334C10.5866 10.7942 10.2135 11.1673 9.75326 11.1673C9.29302 11.1673 8.91992 10.7942 8.91992 10.334C8.91992 9.87375 9.29302 9.50065 9.75326 9.50065C10.2135 9.50065 10.5866 9.87375 10.5866 10.334Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-date calendar-date-4"
        d="M10.5866 13.6673C10.5866 14.1276 10.2135 14.5007 9.75326 14.5007C9.29302 14.5007 8.91992 14.1276 8.91992 13.6673C8.91992 13.2071 9.29302 12.834 9.75326 12.834C10.2135 12.834 10.5866 13.2071 10.5866 13.6673Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-date calendar-date-5"
        d="M5.58659 11.1673C6.04683 11.1673 6.41992 10.7942 6.41992 10.334C6.41992 9.87375 6.04683 9.50065 5.58659 9.50065C5.12635 9.50065 4.75326 9.87375 4.75326 10.334C4.75326 10.7942 5.12635 11.1673 5.58659 11.1673Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-date calendar-date-6"
        d="M5.58659 14.5007C6.04683 14.5007 6.41992 14.1276 6.41992 13.6673C6.41992 13.2071 6.04683 12.834 5.58659 12.834C5.12635 12.834 4.75326 13.2071 4.75326 13.6673C4.75326 14.1276 5.12635 14.5007 5.58659 14.5007Z"
        fill={color || '#384254'}
      />
      <path
        className="calendar-body"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.58659 0.958984C5.93177 0.958984 6.21159 1.23881 6.21159 1.58398V2.21958C6.76325 2.20897 7.37103 2.20898 8.03947 2.20898H11.4669C12.1354 2.20898 12.7433 2.20897 13.2949 2.21958V1.58398C13.2949 1.23881 13.5747 0.958984 13.9199 0.958984C14.2651 0.958984 14.5449 1.23881 14.5449 1.58398V2.27322C14.7615 2.28974 14.9667 2.3105 15.1608 2.3366C16.1378 2.46795 16.9286 2.74472 17.5522 3.36835C18.1759 3.99199 18.4526 4.78278 18.584 5.7598C18.7116 6.70914 18.7116 7.92215 18.7116 9.4536V11.2143C18.7116 12.7458 18.7116 13.9588 18.584 14.9082C18.4526 15.8852 18.1759 16.676 17.5522 17.2996C16.9286 17.9233 16.1378 18.2 15.1608 18.3314C14.2114 18.459 12.9984 18.459 11.467 18.459H8.03958C6.50813 18.459 5.29507 18.459 4.34574 18.3314C3.36872 18.2 2.57793 17.9233 1.95429 17.2996C1.33066 16.676 1.05389 15.8852 0.922534 14.9082C0.794899 13.9588 0.794909 12.7458 0.794922 11.2143V9.45364C0.794909 7.92217 0.794899 6.70914 0.922534 5.7598C1.05389 4.78278 1.33066 3.99199 1.95429 3.36835C2.57793 2.74472 3.36872 2.46795 4.34574 2.3366C4.53984 2.3105 4.74497 2.28974 4.96159 2.27322V1.58398C4.96159 1.23881 5.24141 0.958984 5.58659 0.958984ZM4.5123 3.57545C3.67389 3.68817 3.19085 3.89956 2.83817 4.25224C2.4855 4.60491 2.27411 5.08795 2.16139 5.92636C2.1423 6.06835 2.12634 6.21783 2.11299 6.37565H17.3935C17.3802 6.21783 17.3642 6.06835 17.3451 5.92636C17.2324 5.08795 17.021 4.60491 16.6683 4.25224C16.3157 3.89956 15.8326 3.68817 14.9942 3.57545C14.1378 3.46031 13.0089 3.45898 11.4199 3.45898H8.08659C6.49757 3.45898 5.36868 3.46031 4.5123 3.57545ZM2.04492 9.50065C2.04492 8.78897 2.04519 8.16959 2.05583 7.62565H17.4507C17.4613 8.16959 17.4616 8.78897 17.4616 9.50065V11.1673C17.4616 12.7563 17.4603 13.8852 17.3451 14.7416C17.2324 15.58 17.021 16.0631 16.6683 16.4157C16.3157 16.7684 15.8326 16.9798 14.9942 17.0925C14.1378 17.2077 13.0089 17.209 11.4199 17.209H8.08659C6.49757 17.209 5.36868 17.2077 4.5123 17.0925C3.67389 16.9798 3.19085 16.7684 2.83817 16.4157C2.4855 16.0631 2.27411 15.58 2.16139 14.7416C2.04625 13.8852 2.04492 12.7563 2.04492 11.1673V9.50065Z"
        fill={color || '#384254'}
      />
    </svg>
  );
};

export { CalendarIcon };

// Animated LocationIcon Component
const LocationIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-location-icon ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      <style>
        {`
          .animated-location-icon {
            animation: locationBounce 2.5s ease-in-out infinite;
          }

          .location-pin-1 {
            animation: locationPinPulse 2s ease-in-out infinite;
            transform-origin: center;
          }

          .location-pin-2 {
            animation: locationPinPulse 2s ease-in-out infinite 0.5s;
            transform-origin: center;
          }

          .location-path {
            animation: locationPathFlow 3s ease-in-out infinite;
            stroke-dasharray: 20;
            stroke-dashoffset: 40;
          }

          .location-main {
            animation: locationMainFloat 2.8s ease-in-out infinite;
          }

          @keyframes locationBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-2px); }
          }

          @keyframes locationPinPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
          }

          @keyframes locationPathFlow {
            0% { stroke-dashoffset: 40; }
            100% { stroke-dashoffset: 0; }
          }

          @keyframes locationMainFloat {
            0%, 100% { transform: translateY(0px); }
            33% { transform: translateY(-1px); }
            66% { transform: translateY(1px); }
          }
        `}
      </style>
      <path
        className="location-pin-2"
        d="M16.2501 15.0827C16.2501 15.5429 15.877 15.916 15.4167 15.916C14.9565 15.916 14.5834 15.5429 14.5834 15.0827C14.5834 14.6224 14.9565 14.2494 15.4167 14.2494C15.877 14.2494 16.2501 14.6224 16.2501 15.0827Z"
        fill={color || '#384254'}
      />
      <path
        className="location-pin-1"
        d="M5.41675 5.08268C5.41675 5.54292 5.04365 5.91602 4.58341 5.91602C4.12318 5.91602 3.75008 5.54292 3.75008 5.08268C3.75008 4.62245 4.12318 4.24935 4.58341 4.24935C5.04365 4.24935 5.41675 4.62245 5.41675 5.08268Z"
        fill={color || '#384254'}
      />
      <path
        className="location-main"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.04175 4.88045C1.04175 2.99459 2.67046 1.54102 4.58341 1.54102C6.49637 1.54102 8.12508 2.99459 8.12508 4.88045C8.12508 6.56901 7.08691 8.56192 5.36238 9.29889C4.86693 9.51062 4.2999 9.51062 3.80445 9.29889C2.07992 8.56192 1.04175 6.56901 1.04175 4.88045ZM4.58341 2.79102C3.27471 2.79102 2.29175 3.76802 2.29175 4.88045C2.29175 6.16667 3.11537 7.64506 4.29566 8.14945C4.47736 8.2271 4.68947 8.2271 4.87117 8.14945C6.05146 7.64506 6.87508 6.16667 6.87508 4.88045C6.87508 3.76802 5.89212 2.79102 4.58341 2.79102ZM9.37508 4.66602C9.37508 4.32084 9.6549 4.04102 10.0001 4.04102H13.4433C15.736 4.04102 16.6079 7.03518 14.6737 8.26607L5.9976 13.7872C5.11839 14.3467 5.51472 15.7077 6.55684 15.7077H8.4912L8.30814 15.5246C8.06406 15.2805 8.06406 14.8848 8.30814 14.6407C8.55222 14.3967 8.94795 14.3967 9.19202 14.6407L10.442 15.8907C10.6861 16.1348 10.6861 16.5305 10.442 16.7746L9.19202 18.0246C8.94795 18.2687 8.55222 18.2687 8.30814 18.0246C8.06406 17.7805 8.06406 17.3848 8.30814 17.1407L8.4912 16.9577H6.55684C4.26414 16.9577 3.39226 13.9635 5.32651 12.7326L14.0026 7.2115C14.8818 6.652 14.4855 5.29102 13.4433 5.29102H10.0001C9.6549 5.29102 9.37508 5.01119 9.37508 4.66602ZM11.8751 14.8804C11.8751 12.9946 13.5038 11.541 15.4167 11.541C17.3297 11.541 18.9584 12.9946 18.9584 14.8804C18.9584 16.569 17.9202 18.5619 16.1957 19.2989C15.7003 19.5106 15.1332 19.5106 14.6378 19.2989C12.9133 18.5619 11.8751 16.569 11.8751 14.8804ZM15.4167 12.791C14.108 12.791 13.1251 13.768 13.1251 14.8804C13.1251 16.1667 13.9487 17.6451 15.129 18.1494C15.3107 18.2271 15.5228 18.2271 15.7045 18.1494C16.8848 17.6451 17.7084 16.1667 17.7084 14.8804C17.7084 13.768 16.7255 12.791 15.4167 12.791Z"
        fill={color || '#384254'}
      />
    </svg>
  );
};

export { LocationIcon };

// Animated PaletteIcon Component
const PaletteIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-palette-icon ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      <style>
        {`
          .animated-palette-icon {
            animation: paletteRotate 4s ease-in-out infinite;
          }

          .palette-brush {
            animation: paletteBrushStroke 2.5s ease-in-out infinite;
            transform-origin: center;
          }

          .palette-handle {
            animation: paletteHandleFloat 3s ease-in-out infinite;
          }

          .palette-paint {
            animation: palettePaintFlow 2s ease-in-out infinite;
            stroke-dasharray: 15;
            stroke-dashoffset: 30;
          }

          .palette-dot {
            animation: paletteDotPulse 1.8s ease-in-out infinite;
            transform-origin: center;
          }

          @keyframes paletteRotate {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(2deg); }
            75% { transform: rotate(-2deg); }
          }

          @keyframes paletteBrushStroke {
            0%, 100% { transform: translateX(0px) rotate(0deg); }
            50% { transform: translateX(1px) rotate(2deg); }
          }

          @keyframes paletteHandleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-1px); }
          }

          @keyframes palettePaintFlow {
            0% { stroke-dashoffset: 30; opacity: 0.6; }
            50% { stroke-dashoffset: 0; opacity: 1; }
            100% { stroke-dashoffset: -30; opacity: 0.6; }
          }

          @keyframes paletteDotPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
          }
        `}
      </style>
      <path
        className="palette-handle"
        d="M1.92603 5.49935C1.92603 3.6584 3.41841 2.16602 5.25936 2.16602C7.10031 2.16602 8.59269 3.6584 8.59269 5.49935V15.4993C8.59269 17.3403 7.10031 18.8327 5.25936 18.8327C3.41841 18.8327 1.92603 17.3403 1.92603 15.4993V5.49935Z"
        stroke={color || '#384254'}
        strokeWidth="1.5"
      />
      <path
        className="palette-brush"
        d="M8.59266 7.36856L11.354 4.60717C12.6558 3.30542 14.7663 3.30542 16.0681 4.60717C17.3698 5.90892 17.3698 8.01947 16.0681 9.32122L8.01465 17.3747"
        stroke={color || '#384254'}
        strokeWidth="1.5"
      />
      <path
        className="palette-paint"
        d="M5.25928 18.8327L15.2593 18.8327C17.1002 18.8327 18.5926 17.3403 18.5926 15.4993C18.5926 13.6584 17.1002 12.166 15.2593 12.166L13.1759 12.166"
        stroke={color || '#384254'}
        strokeWidth="1.5"
      />
      <path
        className="palette-dot"
        d="M6.09269 15.4993C6.09269 15.9596 5.7196 16.3327 5.25936 16.3327C4.79912 16.3327 4.42603 15.9596 4.42603 15.4993C4.42603 15.0391 4.79912 14.666 5.25936 14.666C5.7196 14.666 6.09269 15.0391 6.09269 15.4993Z"
        stroke={color || '#384254'}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export { PaletteIcon };

// Animated UserGroupIcon Component
const UserGroupIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-usergroup-icon ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      <style>
        {`
          .animated-usergroup-icon {
            animation: usergroupGather 3s ease-in-out infinite;
          }

          .usergroup-main {
            animation: usergroupMainPulse 2.5s ease-in-out infinite;
            transform-origin: center;
          }

          .usergroup-side-left {
            animation: usergroupSideFloat 2.8s ease-in-out infinite;
            transform-origin: center;
          }

          .usergroup-side-right {
            animation: usergroupSideFloat 2.8s ease-in-out infinite 0.5s;
            transform-origin: center;
          }

          .usergroup-base {
            animation: usergroupBaseExpand 3.2s ease-in-out infinite;
            transform-origin: center;
          }

          .usergroup-connection-left {
            animation: usergroupConnectionPulse 2s ease-in-out infinite;
          }

          .usergroup-connection-right {
            animation: usergroupConnectionPulse 2s ease-in-out infinite 0.3s;
          }

          @keyframes usergroupGather {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
          }

          @keyframes usergroupMainPulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.9; }
          }

          @keyframes usergroupSideFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-1px) scale(1.03); }
          }

          @keyframes usergroupBaseExpand {
            0%, 100% { transform: scaleX(1); }
            50% { transform: scaleX(1.02); }
          }

          @keyframes usergroupConnectionPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
        `}
      </style>
      <g clipPath="url(#clip0_497_7987)">
        <path
          className="usergroup-main"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.5063 1.54102C8.32014 1.54102 6.54793 3.31322 6.54793 5.49935C6.54793 7.68548 8.32014 9.45768 10.5063 9.45768C12.6924 9.45768 14.4646 7.68548 14.4646 5.49935C14.4646 3.31322 12.6924 1.54102 10.5063 1.54102ZM7.79793 5.49935C7.79793 4.00358 9.01049 2.79102 10.5063 2.79102C12.002 2.79102 13.2146 4.00358 13.2146 5.49935C13.2146 6.99512 12.002 8.20768 10.5063 8.20768C9.01049 8.20768 7.79793 6.99512 7.79793 5.49935Z"
          fill={color || '#384254'}
        />
        <path
          className="usergroup-side-right"
          d="M15.5063 3.20768C15.1611 3.20768 14.8813 3.4875 14.8813 3.83268C14.8813 4.17786 15.1611 4.45768 15.5063 4.45768C16.6533 4.45768 17.3813 5.21246 17.3813 5.91602C17.3813 6.61957 16.6533 7.37435 15.5063 7.37435C15.1611 7.37435 14.8813 7.65417 14.8813 7.99935C14.8813 8.34453 15.1611 8.62435 15.5063 8.62435C17.1206 8.62435 18.6313 7.51364 18.6313 5.91602C18.6313 4.31839 17.1206 3.20768 15.5063 3.20768Z"
          fill={color || '#384254'}
        />
        <path
          className="usergroup-side-left"
          d="M6.13127 3.83268C6.13127 3.4875 5.85144 3.20768 5.50627 3.20768C3.89191 3.20768 2.38127 4.31839 2.38127 5.91602C2.38127 7.51364 3.89191 8.62435 5.50627 8.62435C5.85144 8.62435 6.13127 8.34453 6.13127 7.99935C6.13127 7.65417 5.85144 7.37435 5.50627 7.37435C4.35919 7.37435 3.63127 6.61957 3.63127 5.91602C3.63127 5.21246 4.35919 4.45768 5.50627 4.45768C5.85144 4.45768 6.13127 4.17786 6.13127 3.83268Z"
          fill={color || '#384254'}
        />
        <path
          className="usergroup-base"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.5063 10.7077C9.01937 10.7077 7.64497 11.1083 6.62404 11.789C5.60748 12.4667 4.88127 13.4716 4.88127 14.666C4.88127 15.8605 5.60748 16.8654 6.62404 17.5431C7.64497 18.2237 9.01937 18.6243 10.5063 18.6243C11.9932 18.6243 13.3676 18.2237 14.3885 17.5431C15.405 16.8654 16.1313 15.8605 16.1313 14.666C16.1313 13.4716 15.405 12.4667 14.3885 11.789C13.3676 11.1083 11.9932 10.7077 10.5063 10.7077ZM6.13127 14.666C6.13127 14.0195 6.52434 13.3577 7.31742 12.829C8.10613 12.3032 9.23173 11.9577 10.5063 11.9577C11.7808 11.9577 12.9064 12.3032 13.6951 12.829C14.4882 13.3577 14.8813 14.0195 14.8813 14.666C14.8813 15.3125 14.4882 15.9743 13.6951 16.503C12.9064 17.0288 11.7808 17.3743 10.5063 17.3743C9.23173 17.3743 8.10613 17.0288 7.31742 16.503C6.52434 15.9743 6.13127 15.3125 6.13127 14.666Z"
          fill={color || '#384254'}
        />
        <path
          className="usergroup-connection-right"
          d="M16.5624 12.0321C16.6364 11.695 16.9696 11.4816 17.3068 11.5555C18.1084 11.7313 18.8307 12.0488 19.3669 12.4876C19.9027 12.926 20.2979 13.5287 20.2979 14.2493C20.2979 14.97 19.9027 15.5727 19.3669 16.0111C18.8307 16.4499 18.1084 16.7674 17.3068 16.9432C16.9696 17.0171 16.6364 16.8037 16.5624 16.4666C16.4885 16.1294 16.7019 15.7961 17.0391 15.7222C17.6994 15.5774 18.2271 15.3287 18.5753 15.0437C18.9241 14.7583 19.0479 14.4796 19.0479 14.2493C19.0479 14.0191 18.9241 13.7404 18.5753 13.455C18.2271 13.17 17.6994 12.9213 17.0391 12.7765C16.7019 12.7026 16.4885 12.3693 16.5624 12.0321Z"
          fill={color || '#384254'}
        />
        <path
          className="usergroup-connection-left"
          d="M3.70572 11.5555C4.04289 11.4816 4.37615 11.695 4.45009 12.0321C4.52403 12.3693 4.31064 12.7026 3.97348 12.7765C3.31317 12.9213 2.78548 13.17 2.4372 13.455C2.08846 13.7404 1.9646 14.0191 1.9646 14.2493C1.9646 14.4796 2.08846 14.7583 2.4372 15.0437C2.78548 15.3287 3.31317 15.5774 3.97348 15.7222C4.31064 15.7961 4.52403 16.1294 4.45009 16.4666C4.37615 16.8037 4.04289 17.0171 3.70572 16.9432C2.90415 16.7674 2.18185 16.4499 1.64559 16.0111C1.10981 15.5727 0.7146 14.97 0.7146 14.2493C0.7146 13.5287 1.10981 12.926 1.64559 12.4876C2.18185 12.0488 2.90415 11.7313 3.70572 11.5555Z"
          fill={color || '#384254'}
        />
      </g>
      <defs>
        <clipPath id="clip0_497_7987">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.506348 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export { UserGroupIcon };

// Animated BellIcon Component
const BellIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  isAnimation = true,
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${isAnimation ? 'animated-bell-icon' : ''} ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      {isAnimation && (
        <style>
          {`
            .animated-bell-icon {
              animation: bellRing 2.5s ease-in-out infinite;
            }

            .bell-body {
              animation: bellSwing 2s ease-in-out infinite;
              transform-origin: 12px 8px;
            }

            @keyframes bellRing {
              0%, 100% { transform: rotate(0deg); }
              10% { transform: rotate(10deg); }
              20% { transform: rotate(-8deg); }
              30% { transform: rotate(6deg); }
              40% { transform: rotate(-4deg); }
              50% { transform: rotate(2deg); }
              60% { transform: rotate(0deg); }
            }

            @keyframes bellSwing {
              0%, 100% { transform: rotate(0deg); }
              25% { transform: rotate(2deg); }
              75% { transform: rotate(-2deg); }
            }
          `}
        </style>
      )}
      <path
        className={isAnimation ? "bell-body" : ""}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1172 2.23047C7.83702 2.23047 4.36723 5.70026 4.36723 9.98047V10.6846C4.36723 11.3815 4.16094 12.0628 3.77436 12.6427L2.62575 14.3656C1.29266 16.3652 2.31036 19.0833 4.62895 19.7156C5.38457 19.9217 6.14656 20.096 6.91297 20.2386L6.91486 20.2437C7.68386 22.2956 9.73917 23.7305 12.1172 23.7305C14.4952 23.7305 16.5505 22.2956 17.3195 20.2437L17.3214 20.2386C18.0878 20.096 18.8498 19.9217 19.6055 19.7156C21.9241 19.0833 22.9418 16.3652 21.6087 14.3656L20.4601 12.6427C20.0735 12.0628 19.8672 11.3815 19.8672 10.6846V9.98047C19.8672 5.70026 16.3974 2.23047 12.1172 2.23047ZM15.4936 20.5174C13.2507 20.7854 10.9836 20.7854 8.74068 20.5174C9.45163 21.5389 10.6882 22.2305 12.1172 22.2305C13.5461 22.2305 14.7827 21.539 15.4936 20.5174ZM5.86723 9.98047C5.86723 6.52869 8.66545 3.73047 12.1172 3.73047C15.569 3.73047 18.3672 6.52869 18.3672 9.98047V10.6846C18.3672 11.6776 18.6612 12.6485 19.212 13.4747L20.3606 15.1977C21.1258 16.3454 20.5417 17.9055 19.2108 18.2685C14.5666 19.5351 9.66788 19.5351 5.02363 18.2685C3.6928 17.9055 3.10865 16.3454 3.87383 15.1976L5.02243 13.4747C5.57328 12.6485 5.86723 11.6776 5.86723 10.6846V9.98047Z"
        fill={color || "#333333"}
      />
    </svg>
  );
};

export { BellIcon };

// Animated ChatIcon Component
const ChatIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  isAnimation = true,
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${isAnimation ? 'animated-chat-icon' : ''} ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      {isAnimation && (
        <style>
          {`
            .animated-chat-icon {
              animation: chatBubble 2.5s ease-in-out infinite;
            }

            .chat-dot {
              animation: chatDotBounce 1.5s ease-in-out infinite;
              transform-origin: center;
            }

            .chat-dot-1 {
              animation-delay: 0s;
            }

            .chat-dot-2 {
              animation-delay: 0.2s;
            }

            .chat-dot-3 {
              animation-delay: 0.4s;
            }

            .chat-bubble {
              animation: chatBubbleFloat 3s ease-in-out infinite;
            }

            @keyframes chatBubble {
              0%, 100% { transform: scale(1); }
              50% { transform: scale(1.02); }
            }

            @keyframes chatDotBounce {
              0%, 100% { transform: scale(1) translateY(0px); }
              50% { transform: scale(1.2) translateY(-1px); }
            }

            @keyframes chatBubbleFloat {
              0%, 100% { transform: translateY(0px); }
              50% { transform: translateY(-1px); }
            }
          `}
        </style>
      )}
      <path
        className={isAnimation ? "chat-dot chat-dot-1" : ""}
        d="M9.11719 12.9805C9.11719 13.5328 8.66947 13.9805 8.11719 13.9805C7.5649 13.9805 7.11719 13.5328 7.11719 12.9805C7.11719 12.4282 7.5649 11.9805 8.11719 11.9805C8.66947 11.9805 9.11719 12.4282 9.11719 12.9805Z"
        fill={color || "#333333"}
      />
      <path
        className={isAnimation ? "chat-dot chat-dot-2" : ""}
        d="M13.1172 12.9805C13.1172 13.5328 12.6695 13.9805 12.1172 13.9805C11.5649 13.9805 11.1172 13.5328 11.1172 12.9805C11.1172 12.4282 11.5649 11.9805 12.1172 11.9805C12.6695 11.9805 13.1172 12.4282 13.1172 12.9805Z"
        fill={color || "#333333"}
      />
      <path
        className={isAnimation ? "chat-dot chat-dot-3" : ""}
        d="M17.1172 12.9805C17.1172 13.5328 16.6695 13.9805 16.1172 13.9805C15.5649 13.9805 15.1172 13.5328 15.1172 12.9805C15.1172 12.4282 15.5649 11.9805 16.1172 11.9805C16.6695 11.9805 17.1172 12.4282 17.1172 12.9805Z"
        fill={color || "#333333"}
      />
      <path
        className={isAnimation ? "chat-bubble" : ""}
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.8672 12.9805C22.8672 7.04341 18.0542 2.23047 12.1172 2.23047C6.18013 2.23047 1.36719 7.04341 1.36719 12.9805C1.36719 14.6988 1.7709 16.3249 2.48931 17.7673C2.59652 17.9826 2.61926 18.2023 2.56979 18.3872L1.97418 20.6133C1.56646 22.1371 2.96055 23.5312 4.48439 23.1235L6.71043 22.5279C6.89532 22.4784 7.1151 22.5011 7.33034 22.6083C8.77271 23.3268 10.3989 23.7305 12.1172 23.7305C18.0542 23.7305 22.8672 18.9175 22.8672 12.9805ZM12.1172 3.73047C17.2258 3.73047 21.3672 7.87183 21.3672 12.9805C21.3672 18.0891 17.2258 22.2305 12.1172 22.2305C10.6361 22.2305 9.23839 21.8829 7.99909 21.2657C7.50169 21.0179 6.90692 20.9225 6.32272 21.0788L4.09668 21.6744C3.68785 21.7838 3.31382 21.4098 3.42321 21.001L4.01882 18.7749C4.17513 18.1907 4.07973 17.596 3.83198 17.0986C3.21471 15.8593 2.86719 14.4615 2.86719 12.9805C2.86719 7.87183 7.00855 3.73047 12.1172 3.73047Z"
        fill={color || "#333333"}
      />
    </svg>
  );
};

export { ChatIcon };

// Animated Plane Icon MapPointIcon
const MapPointIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_497_9229)">
        <path
          d="M3.5885 7.14566C3.5885 4.14738 5.97632 1.7168 8.92183 1.7168C11.8674 1.7168 14.2552 4.14738 14.2552 7.14566C14.2552 10.1204 12.553 13.5917 9.89712 14.8331C9.27801 15.1225 8.56566 15.1225 7.94655 14.8331C5.29072 13.5917 3.5885 10.1204 3.5885 7.14566Z"
          stroke="#8E8E8E"
          stroke-width="1.5"
        />
        <ellipse
          cx="8.92188"
          cy="7.04883"
          rx="2"
          ry="2"
          stroke="#8E8E8E"
          stroke-width="1.5"
        />
      </g>
      <defs>
        <clipPath id="clip0_497_9229">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.921875 0.382812)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export { MapPointIcon };

// Animated Plane Icon MicrophoneIcon
const MicrophoneIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 7.50977C5 4.74834 7.23858 2.50977 10 2.50977C12.7614 2.50977 15 4.74834 15 7.50977V11.6764C15 14.4379 12.7614 16.6764 10 16.6764C7.23858 16.6764 5 14.4379 5 11.6764V7.50977Z"
        stroke="#1C274C"
        stroke-width="1.5"
      />
      <path
        d="M8.33337 6.25977C8.33337 6.25977 8.72731 5.8431 10 5.8431C11.2728 5.8431 11.6667 6.25977 11.6667 6.25977"
        stroke="#1C274C"
        stroke-width="1.5"
        stroke-linecap="round"
      />
      <path
        d="M8.33337 8.75977C8.33337 8.75977 8.72731 8.3431 10 8.3431C11.2728 8.3431 11.6667 8.75977 11.6667 8.75977"
        stroke="#1C274C"
        stroke-width="1.5"
        stroke-linecap="round"
      />
      <path
        d="M17.5 10.0098V11.6764C17.5 15.8186 14.1421 19.1764 10 19.1764C5.85786 19.1764 2.5 15.8186 2.5 11.6764V10.0098"
        stroke="#1C274C"
        stroke-width="1.5"
        stroke-linecap="round"
      />
    </svg>
  );
};

export { MicrophoneIcon };

// Animated SearchIcon Component
const SearchIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
  isAnimation = true,
}) => {
  return (
    <svg
      width={Math.max(size, 20)}
      height={Math.max(size, 20)}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${isAnimation ? 'animated-search-icon' : ''} ${className}`}
      style={{ minWidth: '20px', minHeight: '20px' }}
    >
      {isAnimation && (
        <style>
          {`
            .animated-search-icon {
              animation: searchPulse 2s ease-in-out infinite;
            }

            .search-circle {
              animation: searchCircleExpand 2.5s ease-in-out infinite;
              transform-origin: center;
            }

            .search-handle {
              animation: searchHandleWiggle 1.8s ease-in-out infinite;
              transform-origin: 17px 17px;
            }

            @keyframes searchPulse {
              0%, 100% { transform: scale(1); }
              50% { transform: scale(1.05); }
            }

            @keyframes searchCircleExpand {
              0%, 100% { transform: scale(1); stroke-width: 1.5; }
              50% { transform: scale(1.02); stroke-width: 2; }
            }

            @keyframes searchHandleWiggle {
              0%, 100% { transform: rotate(0deg); }
              25% { transform: rotate(5deg); }
              75% { transform: rotate(-5deg); }
            }
          `}
        </style>
      )}
      <g clipPath="url(#clip0_497_10512)">
        <circle
          className={isAnimation ? "search-circle" : ""}
          cx="9.16663"
          cy="10.1465"
          r="7.5"
          stroke={color || "white"}
          strokeWidth="1.5"
        />
        <path
          className={isAnimation ? "search-handle" : ""}
          d="M18.1767 18.4601C18.1245 18.539 18.03 18.6334 17.8412 18.8223C17.6523 19.0111 17.5579 19.1055 17.479 19.1578C17.0169 19.4638 16.3916 19.3061 16.13 18.8175C16.0853 18.734 16.047 18.6061 15.9703 18.3503C15.8866 18.0707 15.8447 17.931 15.8366 17.8326C15.7889 17.2535 16.2724 16.77 16.8515 16.8177C16.9499 16.8258 17.0897 16.8676 17.3692 16.9514C17.625 17.0281 17.7529 17.0664 17.8364 17.1111C18.325 17.3727 18.4828 17.998 18.1767 18.4601Z"
          stroke={color || "white"}
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_497_10512">
          <rect width="20" height="20" fill="white" transform="translate(0 0.980469)"/>
        </clipPath>
      </defs>
    </svg>
  );
};

export { SearchIcon };


// Animated Plane Icon SettingIcon
const SettingIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.1172 9.23047C10.0461 9.23047 8.36719 10.9094 8.36719 12.9805C8.36719 15.0515 10.0461 16.7305 12.1172 16.7305C14.1883 16.7305 15.8672 15.0515 15.8672 12.9805C15.8672 10.9094 14.1883 9.23047 12.1172 9.23047ZM9.86719 12.9805C9.86719 11.7378 10.8745 10.7305 12.1172 10.7305C13.3598 10.7305 14.3672 11.7378 14.3672 12.9805C14.3672 14.2231 13.3598 15.2305 12.1172 15.2305C10.8745 15.2305 9.86719 14.2231 9.86719 12.9805Z" fill="#333333"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.0919 2.23047C11.6475 2.23046 11.2764 2.23046 10.9718 2.25124C10.6547 2.27287 10.3552 2.31952 10.0648 2.4398C9.39097 2.71891 8.85562 3.25426 8.57651 3.92809C8.43121 4.27888 8.39185 4.64859 8.37683 5.05043C8.36475 5.37346 8.20172 5.64298 7.96114 5.78188C7.72055 5.92078 7.40564 5.9272 7.11985 5.77615C6.76432 5.58824 6.42447 5.43746 6.04802 5.3879C5.32491 5.2927 4.59361 5.48866 4.01498 5.93265C3.76562 6.124 3.57546 6.36007 3.39817 6.62387C3.22787 6.87728 3.04235 7.19861 2.82013 7.58353L2.79487 7.62728C2.57264 8.01219 2.38711 8.33351 2.25281 8.6077C2.113 8.89314 2.00363 9.17585 1.9626 9.48748C1.8674 10.2106 2.06335 10.9419 2.50735 11.5205C2.73847 11.8217 3.03892 12.0406 3.37936 12.2545C3.65314 12.4266 3.80507 12.7026 3.80505 12.9805C3.80503 13.2583 3.65311 13.5343 3.37936 13.7063C3.03888 13.9202 2.73839 14.1391 2.50725 14.4404C2.06326 15.019 1.8673 15.7503 1.9625 16.4734C2.00353 16.785 2.1129 17.0677 2.25271 17.3532C2.38702 17.6274 2.57254 17.9487 2.79477 18.3336L2.82003 18.3773C3.04225 18.7623 3.22777 19.0836 3.39807 19.337C3.57536 19.6008 3.76552 19.8369 4.01488 20.0282C4.59351 20.4722 5.32482 20.6682 6.04792 20.573C6.42436 20.5234 6.76418 20.3727 7.11969 20.1848C7.40552 20.0337 7.72048 20.0401 7.96109 20.179C8.2017 20.3179 8.36475 20.5875 8.37683 20.9106C8.39186 21.3124 8.43121 21.6821 8.57651 22.0328C8.85562 22.7067 9.39097 23.242 10.0648 23.5211C10.3552 23.6414 10.6547 23.6881 10.9718 23.7097C11.2764 23.7305 11.6475 23.7305 12.0919 23.7305H12.1424C12.5869 23.7305 12.9579 23.7305 13.2625 23.7097C13.5796 23.6881 13.8792 23.6414 14.1696 23.5211C14.8434 23.242 15.3787 22.7067 15.6578 22.0328C15.8032 21.682 15.8425 21.3123 15.8575 20.9105C15.8696 20.5874 16.0326 20.3179 16.2732 20.179C16.5138 20.0401 16.8288 20.0336 17.1146 20.1847C17.4701 20.3726 17.8099 20.5233 18.1863 20.5729C18.9094 20.6681 19.6408 20.4721 20.2194 20.0281C20.4687 19.8368 20.6589 19.6007 20.8362 19.3369C21.0065 19.0835 21.192 18.7622 21.4142 18.3773L21.4395 18.3336C21.6617 17.9487 21.8472 17.6273 21.9816 17.3531C22.1214 17.0677 22.2307 16.7849 22.2718 16.4733C22.367 15.7502 22.171 15.0189 21.727 14.4403C21.4959 14.1391 21.1954 13.9201 20.8549 13.7062C20.5812 13.5342 20.4293 13.2582 20.4293 12.9804C20.4293 12.7026 20.5812 12.4267 20.8549 12.2547C21.1954 12.0408 21.496 11.8218 21.7271 11.5206C22.1711 10.942 22.3671 10.2107 22.2719 9.48755C22.2308 9.17592 22.1215 8.8932 21.9816 8.60777C21.8474 8.3336 21.6618 8.0123 21.4396 7.62742L21.4144 7.58365C21.1921 7.19872 21.0066 6.87735 20.8363 6.62394C20.659 6.36014 20.4688 6.12407 20.2195 5.93272C19.6408 5.48872 18.9095 5.29277 18.1864 5.38797C17.81 5.43753 17.4702 5.58829 17.1147 5.77618C16.8288 5.92725 16.5139 5.92083 16.2733 5.78191C16.0327 5.64299 15.8696 5.37344 15.8575 5.05038C15.8425 4.64855 15.8031 4.27887 15.6578 3.92809C15.3787 3.25426 14.8434 2.71891 14.1696 2.4398C13.8792 2.31952 13.5796 2.27287 13.2625 2.25124C12.9579 2.23046 12.5869 2.23046 12.1424 2.23047H12.0919ZM10.6388 3.82562C10.716 3.79366 10.8332 3.76418 11.0739 3.74776C11.3214 3.73088 11.641 3.73047 12.1172 3.73047C12.5934 3.73047 12.913 3.73088 13.1604 3.74776C13.4012 3.76418 13.5184 3.79366 13.5955 3.82562C13.9018 3.95249 14.1452 4.19583 14.272 4.50211C14.312 4.59873 14.3452 4.74934 14.3586 5.10644C14.3882 5.89882 14.7972 6.66176 15.5233 7.08095C16.2493 7.50015 17.1146 7.47287 17.8156 7.10235C18.1315 6.93537 18.2785 6.88879 18.3822 6.87514C18.7109 6.83187 19.0433 6.92094 19.3063 7.12275C19.3726 7.17359 19.4567 7.26036 19.5913 7.46063C19.7297 7.6665 19.8898 7.94307 20.1279 8.35547C20.366 8.76787 20.5255 9.04485 20.6346 9.2676C20.7407 9.48429 20.7738 9.60054 20.7847 9.68334C20.828 10.012 20.7389 10.3444 20.5371 10.6074C20.4734 10.6904 20.3595 10.7945 20.0569 10.9846C19.3856 11.4064 18.9294 12.142 18.9293 12.9803C18.9293 13.8188 19.3855 14.5545 20.0569 14.9763C20.3595 15.1664 20.4733 15.2705 20.537 15.3534C20.7388 15.6164 20.8279 15.9488 20.7846 16.2775C20.7737 16.3603 20.7406 16.4766 20.6345 16.6933C20.5254 16.916 20.3659 17.193 20.1278 17.6054C19.8897 18.0178 19.7296 18.2944 19.5912 18.5002C19.4566 18.7005 19.3725 18.7873 19.3062 18.8381C19.0432 19.0399 18.7108 19.129 18.3821 19.0857C18.2785 19.0721 18.1314 19.0255 17.8155 18.8585C17.1145 18.488 16.2492 18.4608 15.5231 18.88C14.7971 19.2992 14.3882 20.0621 14.3586 20.8544C14.3452 21.2116 14.3121 21.3622 14.272 21.4588C14.1452 21.7651 13.9018 22.0085 13.5955 22.1353C13.5184 22.1673 13.4012 22.1968 13.1604 22.2132C12.913 22.2301 12.5934 22.2305 12.1172 22.2305C11.641 22.2305 11.3214 22.2301 11.0739 22.2132C10.8332 22.1968 10.716 22.1673 10.6388 22.1353C10.3325 22.0085 10.0892 21.7651 9.96233 21.4588C9.92231 21.3622 9.88914 21.2116 9.87578 20.8545C9.84614 20.0621 9.43716 19.2992 8.71109 18.88C7.98502 18.4608 7.11981 18.4881 6.41877 18.8586C6.10284 19.0256 5.95581 19.0721 5.85213 19.0858C5.52345 19.1291 5.19104 19.04 4.92802 18.8382C4.86177 18.7873 4.77763 18.7006 4.64305 18.5003C4.50469 18.2944 4.34455 18.0179 4.10644 17.6055C3.86834 17.1931 3.7089 16.9161 3.59979 16.6933C3.49365 16.4766 3.46057 16.3604 3.44967 16.2776C3.4064 15.9489 3.49547 15.6165 3.69728 15.3535C3.76095 15.2705 3.8748 15.1665 4.17739 14.9764C4.84877 14.5545 5.305 13.8189 5.30505 12.9805C5.3051 12.1421 4.84884 11.4063 4.1774 10.9844C3.87487 10.7944 3.76104 10.6903 3.69738 10.6074C3.49556 10.3444 3.4065 10.012 3.44977 9.68327C3.46067 9.60047 3.49375 9.48422 3.59989 9.26753C3.709 9.04478 3.86844 8.76781 4.10654 8.3554C4.34464 7.943 4.50479 7.66643 4.64314 7.46056C4.77773 7.2603 4.86187 7.17352 4.92812 7.12268C5.19113 6.92087 5.52355 6.8318 5.85223 6.87507C5.95592 6.88872 6.10295 6.9353 6.41892 7.10231C7.11992 7.47282 7.9851 7.50009 8.71113 7.08092C9.43717 6.66175 9.84615 5.89884 9.87578 5.10649C9.88913 4.74936 9.92231 4.59873 9.96233 4.50212C10.0892 4.19583 10.3325 3.95249 10.6388 3.82562Z" fill="#333333"/>
</svg>
  );
};

export { SettingIcon };

// Animated Plane Icon SortIcon
const SortIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.9609 7L4.96094 7" stroke="#777777" stroke-width="1.5" stroke-linecap="round"/>
<path d="M15.9609 12L4.96094 12" stroke="#777777" stroke-width="1.5" stroke-linecap="round"/>
<path d="M9.96094 17H4.96094" stroke="#777777" stroke-width="1.5" stroke-linecap="round"/>
</svg>

  );
};

export { SortIcon };

// Animated Plane Icon StarFallIcon
const StarFallIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7209 17.3586C13.7162 17.2487 13.5342 17.2051 13.4803 17.3009C13.227 17.7506 12.7948 18.4119 12.3045 18.7126C11.8141 19.0134 11.0287 19.0987 10.513 19.1206C10.4032 19.1252 10.3595 19.3072 10.4553 19.3612C10.905 19.6145 11.5664 20.0466 11.8671 20.537C12.1678 21.0273 12.2531 21.8127 12.275 22.3284C12.2797 22.4383 12.4617 22.4819 12.5156 22.3861C12.7689 21.9364 13.2011 21.275 13.6914 20.9743C14.1818 20.6736 14.9672 20.5883 15.4829 20.5664C15.5927 20.5617 15.6364 20.3798 15.5406 20.3258C15.0909 20.0725 14.4295 19.6403 14.1288 19.15C13.8281 18.6596 13.7428 17.8742 13.7209 17.3586Z" fill="#707FF5"/>
<path opacity="0.5" d="M5.51179 16.3585C5.52025 16.2488 5.7121 16.2028 5.76935 16.2967C5.94165 16.5792 6.19012 16.9202 6.4663 17.0895C6.74247 17.2589 7.15903 17.3258 7.48895 17.3513C7.59858 17.3597 7.64457 17.5516 7.5507 17.6088C7.26819 17.7811 6.92723 18.0296 6.75786 18.3058C6.5885 18.5819 6.52162 18.9985 6.49615 19.3284C6.48768 19.4381 6.29583 19.484 6.23858 19.3902C6.06629 19.1077 5.81781 18.7667 5.54164 18.5973C5.26546 18.428 4.8489 18.3611 4.51898 18.3356C4.40936 18.3272 4.36337 18.1353 4.45724 18.0781C4.73975 17.9058 5.08071 17.6573 5.25007 17.3811C5.41944 17.1049 5.48631 16.6884 5.51179 16.3585Z" fill="#707FF5"/>
<path d="M9.29547 4.84556L9.53779 4.53949C10.4744 3.35641 10.9428 2.76487 11.4876 2.8522C12.0324 2.93952 12.293 3.64787 12.814 5.06458L12.9488 5.4311C13.0969 5.83368 13.1709 6.03497 13.3129 6.18272C13.4549 6.33046 13.6488 6.40792 14.0365 6.56286L14.3895 6.70391L14.6373 6.80308C15.8369 7.28435 16.4413 7.55706 16.5185 8.08699C16.6008 8.65224 16.0287 9.1358 14.8845 10.1029L14.5884 10.3531C14.2633 10.6279 14.1007 10.7653 14.0075 10.9526C13.9143 11.1399 13.9011 11.3559 13.8745 11.7878L13.8504 12.1811C13.757 13.7011 13.7103 14.4611 13.2163 14.7232C12.7224 14.9852 12.1083 14.5757 10.8801 13.7567L10.8801 13.7567L10.5623 13.5448C10.2133 13.3121 10.0388 13.1957 9.83921 13.1637C9.63963 13.1317 9.43756 13.1877 9.03343 13.2998L8.6655 13.4017C7.24335 13.796 6.53228 13.9931 6.14467 13.5898C5.75707 13.1864 5.94963 12.4498 6.33477 10.9765L6.43441 10.5954C6.54385 10.1767 6.59857 9.96739 6.56842 9.76032C6.53826 9.55326 6.42665 9.37191 6.20342 9.00921L6.20341 9.00919L6.00018 8.67897C5.21462 7.40258 4.82184 6.76438 5.07625 6.2531C5.33066 5.74181 6.06378 5.69604 7.53001 5.60451L7.90934 5.58083C8.326 5.55482 8.53432 5.54182 8.71527 5.44583C8.89622 5.34985 9.0293 5.18175 9.29547 4.84556L9.29547 4.84556Z" fill="#707FF5"/>
<path opacity="0.5" d="M15.1638 14.1698C17.3011 15.214 19.0795 16.8677 19.7503 18.8439C20.5029 14.1364 19.459 11.0964 17.7857 9.20703C17.6415 9.50175 17.4541 9.74575 17.2858 9.93644C16.9359 10.3329 16.4336 10.7572 15.9235 11.1881L15.5565 11.4983C15.4869 11.5571 15.4328 11.6029 15.3868 11.6426C15.3821 11.707 15.3775 11.7821 15.3715 11.8794L15.3417 12.3649C15.3016 13.0199 15.2625 13.6593 15.1638 14.1698Z" fill="#707FF5"/>
</svg>
  );
};

export { StarFallIcon };

// Animated Plane Icon BagIcon
const BagIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.9485 2.11133H12.052C12.9461 2.1113 13.6916 2.11128 14.2835 2.19085C14.9082 2.27484 15.4723 2.45961 15.9255 2.91285C16.3788 3.3661 16.5635 3.93013 16.6475 4.55486C16.7063 4.99232 16.7217 5.51372 16.7257 6.117C17.371 6.13771 17.9463 6.17545 18.4576 6.24419C19.6243 6.40104 20.5686 6.73154 21.3133 7.47625C22.058 8.22096 22.3885 9.16527 22.5454 10.332C22.6978 11.4656 22.6978 12.9141 22.6978 14.7429V14.8552C22.6978 16.684 22.6978 18.1325 22.5454 19.2662C22.3885 20.4329 22.058 21.3772 21.3133 22.1219C20.5686 22.8666 19.6243 23.1971 18.4576 23.354C17.3239 23.5064 15.8754 23.5064 14.0466 23.5063H9.95387C8.12509 23.5064 6.67656 23.5064 5.54291 23.354C4.37621 23.1971 3.43189 22.8666 2.68718 22.1219C1.94248 21.3772 1.61198 20.4329 1.45512 19.2662C1.30271 18.1325 1.30272 16.684 1.30273 14.8552V14.7429C1.30272 12.9141 1.30271 11.4656 1.45512 10.332C1.61198 9.16527 1.94248 8.22096 2.68718 7.47625C3.43189 6.73154 4.37621 6.40104 5.54291 6.24419C6.05417 6.17545 6.62948 6.13771 7.27483 6.117C7.27881 5.51372 7.29415 4.99232 7.35296 4.55486C7.43695 3.93013 7.62172 3.3661 8.07496 2.91285C8.52821 2.45961 9.09224 2.27484 9.71697 2.19085C10.3088 2.11128 11.0544 2.1113 11.9485 2.11133ZM8.76791 6.09356C9.14419 6.09179 9.53926 6.0918 9.95387 6.0918H14.0466C14.4612 6.0918 14.8563 6.09179 15.2326 6.09356C15.2284 5.52603 15.2144 5.09793 15.1682 4.75376C15.1064 4.29455 14.9997 4.09802 14.87 3.96834C14.7404 3.83865 14.5438 3.73196 14.0846 3.67022C13.6039 3.60559 12.9595 3.60401 12.0002 3.60401C11.0409 3.60401 10.3965 3.60559 9.91586 3.67022C9.45666 3.73196 9.26012 3.83865 9.13044 3.96834C9.00076 4.09802 8.89406 4.29455 8.83232 4.75376C8.78605 5.09793 8.7721 5.52603 8.76791 6.09356ZM5.2832 7.79932C4.55601 7.94529 4.09489 8.1795 3.74267 8.53173C3.32152 8.95287 3.06909 9.52969 2.93449 10.5309C2.797 11.5535 2.79541 12.9016 2.79541 14.7991C2.79541 16.6966 2.797 18.0446 2.93449 19.0673C3.06909 20.0685 3.32152 20.6453 3.74267 21.0664C4.09489 21.4186 4.55601 21.6529 5.2832 21.7988V19.4845C5.08402 19.4457 4.85221 19.3613 4.65242 19.1616C4.41777 18.9269 4.3423 18.6481 4.31296 18.4298C4.28791 18.2435 4.288 18.0228 4.28808 17.8187C4.28808 17.8072 4.28809 17.7958 4.28809 17.7844V16.7893C4.28809 16.7779 4.28808 16.7665 4.28808 16.755C4.288 16.5509 4.28791 16.3302 4.31296 16.1439C4.3423 15.9257 4.41777 15.6468 4.65242 15.4122C4.85221 15.2124 5.08402 15.128 5.2832 15.0892V7.79932ZM6.77588 7.6318V15.0479H10.01C10.0214 15.0479 10.0328 15.0478 10.0443 15.0478C10.2484 15.0478 10.4691 15.0477 10.6554 15.0727C10.8737 15.1021 11.1525 15.1775 11.3871 15.4122C11.6218 15.6468 11.6973 15.9257 11.7266 16.1439C11.7516 16.3302 11.7516 16.5509 11.7515 16.755C11.7515 16.7665 11.7515 16.7779 11.7515 16.7893V17.7844C11.7515 17.7958 11.7515 17.8072 11.7515 17.8187C11.7516 18.0228 11.7516 18.2435 11.7266 18.4298C11.6973 18.6481 11.6218 18.9269 11.3871 19.1616C11.1525 19.3962 10.8737 19.4717 10.6554 19.501C10.4691 19.5261 10.2484 19.526 10.0443 19.5259C10.0328 19.5259 10.0214 19.5259 10.01 19.5259H6.77588V21.9663C7.63884 22.0129 8.69185 22.0137 10.01 22.0137H13.9905C15.3086 22.0137 16.3616 22.0129 17.2246 21.9663V7.6318C16.3617 7.58524 15.3086 7.58448 13.9905 7.58448H10.01C8.69185 7.58448 7.63884 7.58524 6.77588 7.6318ZM18.7173 7.79932V21.7988C19.4445 21.6529 19.9056 21.4186 20.2578 21.0664C20.679 20.6453 20.9314 20.0685 21.066 19.0673C21.2035 18.0446 21.2051 16.6966 21.2051 14.7991C21.2051 12.9016 21.2035 11.5535 21.066 10.5309C20.9314 9.52969 20.679 8.95287 20.2578 8.53173C19.9056 8.1795 19.4445 7.94529 18.7173 7.79932ZM5.78191 16.5417C5.7808 16.6108 5.78076 16.6907 5.78076 16.7893V17.7844C5.78076 17.883 5.7808 17.9629 5.78191 18.0321C5.85103 18.0332 5.93095 18.0332 6.02954 18.0332H10.01C10.1086 18.0332 10.1885 18.0332 10.2576 18.0321C10.2588 17.9629 10.2588 17.883 10.2588 17.7844V16.7893C10.2588 16.6907 10.2588 16.6108 10.2576 16.5417C10.1885 16.5406 10.1086 16.5405 10.01 16.5405H6.02954C5.93095 16.5405 5.85103 16.5406 5.78191 16.5417Z" fill="#1C1C1C"/>
</svg>
  );
};

export { BagIcon };


// Animated Plane Icon HomeIcon
const HomeIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.2539 18.8342C11.2539 19.2464 11.5881 19.5806 12.0002 19.5806C12.4124 19.5806 12.7466 19.2464 12.7466 18.8342V15.8489C12.7466 15.4367 12.4124 15.1025 12.0002 15.1025C11.5881 15.1025 11.2539 15.4367 11.2539 15.8489V18.8342Z" fill="#1C1C1C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.0002 2.16602C11.2956 2.16602 10.6554 2.36784 9.96081 2.70561C9.28939 3.03211 8.51377 3.51349 7.54496 4.11478L5.48848 5.39109C4.57178 5.96001 3.83774 6.41557 3.27197 6.84885C2.6861 7.29753 2.2361 7.75449 1.91106 8.34831C1.5867 8.9409 1.44063 9.57142 1.37059 10.3167C1.30272 11.039 1.30273 11.9223 1.30273 13.0299V14.6347C1.30272 16.5293 1.30271 18.0248 1.45469 19.1941C1.6106 20.3935 1.93818 21.3619 2.67834 22.1275C3.42192 22.8967 4.36815 23.2403 5.53955 23.4032C6.67486 23.5611 8.12482 23.5611 9.95214 23.561H14.0483C15.8756 23.5611 17.3256 23.5611 18.4609 23.4032C19.6323 23.2403 20.5786 22.8967 21.3221 22.1275C22.0623 21.3619 22.3899 20.3935 22.5458 19.1941C22.6978 18.0248 22.6978 16.5293 22.6978 14.6347V13.0299C22.6978 11.9223 22.6978 11.039 22.6299 10.3167C22.5599 9.57142 22.4138 8.9409 22.0894 8.34831C21.7644 7.75449 21.3144 7.29753 20.7285 6.84885C20.1628 6.41557 19.4287 5.96002 18.512 5.39111L16.4555 4.11477C15.4867 3.51349 14.7111 3.03211 14.0397 2.70561C13.3451 2.36784 12.7049 2.16602 12.0002 2.16602ZM8.29794 5.40424C9.30874 4.77691 10.0195 4.3369 10.6136 4.04798C11.1924 3.76649 11.6024 3.65869 12.0002 3.65869C12.3981 3.65869 12.8081 3.76649 13.3869 4.04798C13.981 4.3369 14.6917 4.77691 15.7026 5.40424L17.6928 6.63944C18.6489 7.23285 19.3202 7.65045 19.821 8.03392C20.3082 8.40704 20.5885 8.71494 20.7801 9.06502C20.9724 9.41633 21.0846 9.82708 21.1438 10.4564C21.2043 11.1008 21.2051 11.914 21.2051 13.0664V14.5801C21.2051 16.5414 21.2036 17.9398 21.0656 19.0017C20.93 20.0446 20.6747 20.6497 20.249 21.09C19.8267 21.5268 19.2518 21.7862 18.2553 21.9247C17.2347 22.0667 15.8887 22.0684 13.9905 22.0684H10.01C8.11179 22.0684 6.76576 22.0667 5.74516 21.9247C4.74868 21.7862 4.17378 21.5268 3.75151 21.09C3.32582 20.6497 3.07048 20.0446 2.93492 19.0017C2.79689 17.9398 2.79541 16.5414 2.79541 14.5801V13.0664C2.79541 11.914 2.79617 11.1008 2.85672 10.4564C2.91585 9.82708 3.02812 9.41633 3.22042 9.06502C3.41204 8.71494 3.69233 8.40704 4.17954 8.03392C4.68026 7.65045 5.35156 7.23285 6.3077 6.63944L8.29794 5.40424Z" fill="#1C1C1C"/>
</svg>

  );
};

export { HomeIcon };

// Animated Plane Icon MagicStickIcon
const MagicStickIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.88435 4.16169C2.76381 5.28223 2.76381 7.09898 3.88435 8.21952L5.46433 9.7995C5.4762 9.78579 5.48864 9.7724 5.50167 9.75937L9.48214 5.7789C9.49513 5.76591 9.50848 5.7535 9.52215 5.74167L7.94217 4.16169C6.82164 3.04115 5.00489 3.04115 3.88435 4.16169Z" fill="#707FF5"/>
<path d="M10.5748 6.79437C10.563 6.80804 10.5506 6.82139 10.5376 6.83438L6.55715 10.8148C6.54412 10.8279 6.53073 10.8403 6.51702 10.8522L16.0578 20.393C17.1784 21.5135 18.9951 21.5135 20.1157 20.393C21.2362 19.2725 21.2362 17.4557 20.1157 16.3352L10.5748 6.79437Z" fill="#707FF5"/>
<path d="M16.08 2.63186C16.2402 2.22428 16.8149 2.22428 16.975 2.63186L17.4033 3.7218C17.4522 3.84624 17.5504 3.94474 17.6743 3.99382L18.7603 4.42368C19.1663 4.58443 19.1663 5.16126 18.7603 5.32201L17.6743 5.75188C17.5504 5.80096 17.4522 5.89946 17.4033 6.0239L16.975 7.11383C16.8149 7.52142 16.2402 7.52142 16.08 7.11384L15.6517 6.0239C15.6028 5.89946 15.5047 5.80096 15.3807 5.75188L14.2948 5.32201C13.8887 5.16127 13.8887 4.58443 14.2948 4.42368L15.3807 3.99382C15.5047 3.94474 15.6028 3.84624 15.6517 3.7218L16.08 2.63186Z" fill="#707FF5"/>
<path d="M19.9283 9.42081C20.0884 9.01323 20.6631 9.01323 20.8233 9.42081L20.9795 9.81821C21.0284 9.94265 21.1265 10.0412 21.2505 10.0902L21.6464 10.247C22.0525 10.4077 22.0525 10.9845 21.6464 11.1453L21.2505 11.302C21.1265 11.3511 21.0284 11.4496 20.9795 11.574L20.8233 11.9714C20.6631 12.379 20.0884 12.379 19.9283 11.9714L19.7721 11.574C19.7232 11.4496 19.6251 11.3511 19.5011 11.302L19.1052 11.1453C18.6991 10.9845 18.6991 10.4077 19.1052 10.247L19.5011 10.0902C19.6251 10.0412 19.7232 9.94265 19.7721 9.81821L19.9283 9.42081Z" fill="#707FF5"/>
<path d="M5.16673 15.5684C5.32688 15.1608 5.90159 15.1608 6.06175 15.5684L6.2179 15.9658C6.2668 16.0902 6.36494 16.1887 6.48892 16.2378L6.88485 16.3945C7.29093 16.5553 7.29093 17.1321 6.88485 17.2929L6.48892 17.4496C6.36494 17.4987 6.2668 17.5972 6.2179 17.7216L6.06175 18.119C5.90159 18.5266 5.32688 18.5266 5.16673 18.119L5.01057 17.7216C4.96168 17.5972 4.86354 17.4987 4.73956 17.4496L4.34363 17.2929C3.93754 17.1321 3.93754 16.5553 4.34363 16.3945L4.73956 16.2378C4.86354 16.1887 4.96168 16.0902 5.01057 15.9658L5.16673 15.5684Z" fill="#707FF5"/>
</svg>
  );
};

export { MagicStickIcon };

// Animated Plane Icon MapIcon
const MapIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3 8.70938C3 7.23584 3 6.49907 3.39264 6.06935C3.53204 5.91678 3.70147 5.79466 3.89029 5.71066C4.42213 5.47406 5.12109 5.70705 6.51901 6.17302C7.58626 6.52877 8.11989 6.70665 8.6591 6.68823C8.85714 6.68147 9.05401 6.65511 9.24685 6.60952C9.77191 6.48541 10.2399 6.1734 11.176 5.54937L12.5583 4.62778C13.7574 3.82843 14.3569 3.42876 15.0451 3.3366C15.7333 3.24444 16.4168 3.47229 17.7839 3.92799L18.9487 4.31624C19.9387 4.64625 20.4337 4.81126 20.7169 5.20409C21 5.59692 21 6.11871 21 7.16229V15.2907C21 16.7642 21 17.501 20.6074 17.9307C20.468 18.0833 20.2985 18.2054 20.1097 18.2894C19.5779 18.526 18.8789 18.293 17.481 17.827C16.4137 17.4713 15.8801 17.2934 15.3409 17.3118C15.1429 17.3186 14.946 17.3449 14.7532 17.3905C14.2281 17.5146 13.7601 17.8266 12.824 18.4507L11.4417 19.3722C10.2426 20.1716 9.64311 20.5713 8.95493 20.6634C8.26674 20.7556 7.58319 20.5277 6.21609 20.072L5.05132 19.6838C4.06129 19.3538 3.56627 19.1888 3.28314 18.7959C3 18.4031 3 17.8813 3 16.8377V8.70938Z" stroke="#4A4A4A" stroke-width="1.5"/>
<path d="M9 6.63867V20.5" stroke="#4A4A4A" stroke-width="1.5"/>
<path d="M15 3V17" stroke="#4A4A4A" stroke-width="1.5"/>
</svg>
  );
};

export { MapIcon };

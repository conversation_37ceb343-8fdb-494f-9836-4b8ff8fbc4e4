import React from 'react';

interface AnimatedIconProps {
  size?: number;
  className?: string;
  color?: string;
}

const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-icon {
            animation: pulse 2s ease-in-out infinite;
          }
          
          .icon-path {
            animation: draw 3s ease-in-out infinite;
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
          }
          
          .icon-circle {
            animation: rotate 4s linear infinite;
            transform-origin: 50px 50px;
          }
          
          .icon-heart {
            animation: heartbeat 1.5s ease-in-out infinite;
            transform-origin: 50px 50px;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
          
          @keyframes draw {
            0% { stroke-dashoffset: 200; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -200; }
          }
          
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          
          @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
          }
        `}
      </style>

      {/* Animated Travel/Plane Icon */}
      <g className="icon-circle">
        <circle
          cx="50"
          cy="50"
          r="40"
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeDasharray="5,5"
        />
      </g>

      <g className="icon-heart">
        <path
          d="M50 75 C30 55, 10 35, 30 25 C40 20, 50 30, 50 30 C50 30, 60 20, 70 25 C90 35, 70 55, 50 75 Z"
          fill={color}
          opacity="0.8"
        />
      </g>

      <path
        className="icon-path"
        d="M20 50 Q35 30, 50 50 T80 50"
        fill="none"
        stroke={color}
        strokeWidth="3"
        strokeLinecap="round"
      />

      {/* Animated dots */}
      <circle cx="25" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="50" cy="35" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="0.5s"
        />
      </circle>
      <circle cx="75" cy="45" r="2" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="2s"
          repeatCount="indefinite"
          begin="1s"
        />
      </circle>
    </svg>
  );
};

// Animated Plane Icon Component
const AnimatedPlaneIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={`animated-plane-icon ${className}`}
      xmlns="http://www.w3.org/2000/svg"
    >
      <style>
        {`
          .animated-plane-icon {
            animation: float 3s ease-in-out infinite;
          }

          .plane-body {
            animation: fly 4s linear infinite;
            transform-origin: 50px 50px;
          }

          .plane-trail {
            animation: trail 2s ease-in-out infinite;
            stroke-dasharray: 10;
            stroke-dashoffset: 20;
          }

          .cloud {
            animation: drift 6s ease-in-out infinite;
          }

          .propeller {
            animation: spin 0.1s linear infinite;
            transform-origin: 35px 45px;
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
          }

          @keyframes fly {
            0% { transform: translateX(-10px); }
            50% { transform: translateX(5px); }
            100% { transform: translateX(-10px); }
          }

          @keyframes trail {
            0% { stroke-dashoffset: 20; opacity: 1; }
            100% { stroke-dashoffset: -20; opacity: 0.3; }
          }

          @keyframes drift {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(10px); }
          }

          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>

      {/* Clouds */}
      <g className="cloud">
        <ellipse cx="20" cy="25" rx="8" ry="4" fill={color} opacity="0.3" />
        <ellipse cx="75" cy="20" rx="6" ry="3" fill={color} opacity="0.2" />
      </g>

      {/* Plane body */}
      <g className="plane-body">
        {/* Main fuselage */}
        <ellipse cx="50" cy="45" rx="25" ry="4" fill={color} />

        {/* Wings */}
        <ellipse cx="45" cy="45" rx="15" ry="2" fill={color} />

        {/* Tail */}
        <polygon points="70,45 75,40 75,50" fill={color} />

        {/* Cockpit */}
        <circle cx="30" cy="45" r="3" fill={color} opacity="0.8" />
      </g>

      {/* Propeller */}
      <g className="propeller">
        <line x1="30" y1="45" x2="40" y2="45" stroke={color} strokeWidth="1" />
        <line x1="35" y1="40" x2="35" y2="50" stroke={color} strokeWidth="1" />
      </g>

      {/* Flight trail */}
      <path
        className="plane-trail"
        d="M75 45 Q85 40, 95 45 Q85 50, 75 45"
        fill="none"
        stroke={color}
        strokeWidth="2"
        opacity="0.6"
      />

      {/* Animated stars/sparkles */}
      <circle cx="15" cy="60" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0s"
        />
      </circle>
      <circle cx="85" cy="65" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="0.7s"
        />
      </circle>
      <circle cx="60" cy="75" r="1" fill={color}>
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="1.5s"
          repeatCount="indefinite"
          begin="1.2s"
        />
      </circle>
    </svg>
  );
};

export default AnimatedIcon;
export { AnimatedPlaneIcon };

// Animated HeartIcon Component
const HeartIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-heart-icon ${className}`}
    >
      <style>
        {`
          .animated-heart-icon {
            animation: heartPulse 1.5s ease-in-out infinite;
            transform-origin: center;
          }

          .heart-path {
            animation: heartFill 2s ease-in-out infinite;
          }

          @keyframes heartPulse {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1); }
            50% { transform: scale(1); }
            75% { transform: scale(1.05); }
          }

          @keyframes heartFill {
            0%, 100% { fill: ${color || '#707FF5'}; }
            50% { fill: #FF6B9D; }
          }
        `}
      </style>
      <path
        className="heart-path"
        d="M6.62444 14.7599L7.08876 14.1709L6.62444 14.7599ZM9.15633 3.58471L8.61597 4.10482C8.75735 4.25171 8.95245 4.33471 9.15633 4.33471C9.36021 4.33471 9.55531 4.25171 9.6967 4.10482L9.15633 3.58471ZM11.6882 14.7599L12.1525 15.3489L11.6882 14.7599ZM6.62444 14.7599L7.08876 14.1709C5.81829 13.1694 4.45104 12.2072 3.36503 10.9843C2.30474 9.7904 1.573 8.40726 1.573 6.6151H0.822998H0.072998C0.072998 8.87539 1.01602 10.5982 2.24347 11.9804C3.4452 13.3336 4.97867 14.4175 6.16013 15.3489L6.62444 14.7599ZM0.822998 6.6151H1.573C1.573 4.86948 2.55919 3.41209 3.89526 2.80137C5.18405 2.21226 6.93298 2.35626 8.61597 4.10482L9.15633 3.58471L9.6967 3.06461C7.62982 0.917201 5.21208 0.550169 3.27167 1.43714C1.37854 2.30249 0.072998 4.30828 0.072998 6.6151H0.822998ZM6.62444 14.7599L6.16013 15.3489C6.58584 15.6845 7.0508 16.0489 7.52395 16.3254C7.99686 16.6017 8.54927 16.8342 9.15633 16.8342V16.0842V15.3342C8.93006 15.3342 8.64914 15.2456 8.28074 15.0303C7.91258 14.8151 7.52826 14.5174 7.08876 14.1709L6.62444 14.7599ZM11.6882 14.7599L12.1525 15.3489C13.334 14.4175 14.8675 13.3336 16.0692 11.9804C17.2966 10.5982 18.2397 8.87539 18.2397 6.6151H17.4897H16.7397C16.7397 8.40726 16.0079 9.7904 14.9476 10.9843C13.8616 12.2072 12.4944 13.1694 11.2239 14.1709L11.6882 14.7599ZM17.4897 6.6151H18.2397C18.2397 4.30828 16.9341 2.30249 15.041 1.43714C13.1006 0.550169 10.6828 0.917201 8.61597 3.06461L9.15633 3.58471L9.6967 4.10482C11.3797 2.35626 13.1286 2.21226 14.4174 2.80137C15.7535 3.41209 16.7397 4.86948 16.7397 6.6151H17.4897ZM11.6882 14.7599L11.2239 14.1709C10.7844 14.5174 10.4001 14.8151 10.0319 15.0303C9.66352 15.2456 9.3826 15.3342 9.15633 15.3342V16.0842V16.8342C9.7634 16.8342 10.3158 16.6017 10.7887 16.3254C11.2619 16.0489 11.7268 15.6845 12.1525 15.3489L11.6882 14.7599Z"
        fill="#707FF5"
      />
    </svg>
  );
};

export { HeartIcon };

// Animated DownloadIcon Component
const DownloadIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-download-icon ${className}`}
    >
      <style>
        {`
          .animated-download-icon {
            animation: downloadBounce 2s ease-in-out infinite;
          }

          .download-arrow {
            animation: downloadMove 1.5s ease-in-out infinite;
            transform-origin: center;
          }

          .download-container {
            animation: downloadFade 2s ease-in-out infinite;
          }

          @keyframes downloadBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-2px); }
          }

          @keyframes downloadMove {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(2px); }
          }

          @keyframes downloadFade {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
          }
        `}
      </style>
      <path
        className="download-container"
        d="M2.57812 12.5C2.57812 14.857 2.57812 16.0355 3.31036 16.7678C4.04259 17.5 5.2211 17.5 7.57812 17.5H12.5781C14.9351 17.5 16.1137 17.5 16.8459 16.7678C17.5781 16.0355 17.5781 14.857 17.5781 12.5"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        className="download-arrow"
        d="M10.0782 2.50065V13.334M10.0782 13.334L13.4115 9.68815M10.0782 13.334L6.74487 9.68815"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export { DownloadIcon };

// Animated ShareIcon Component
const ShareIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`animated-share-icon ${className}`}
    >
      <style>
        {`
          .animated-share-icon {
            animation: shareFloat 2.5s ease-in-out infinite;
          }

          .share-node {
            animation: shareNodePulse 2s ease-in-out infinite;
            transform-origin: center;
          }

          .share-connection {
            animation: shareConnectionFlow 1.8s ease-in-out infinite;
            stroke-dasharray: 10;
            stroke-dashoffset: 20;
          }

          .share-node-1 {
            animation-delay: 0s;
          }

          .share-node-2 {
            animation-delay: 0.3s;
          }

          .share-node-3 {
            animation-delay: 0.6s;
          }

          @keyframes shareFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-1px) rotate(1deg); }
          }

          @keyframes shareNodePulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
          }

          @keyframes shareConnectionFlow {
            0% { stroke-dashoffset: 20; opacity: 0.6; }
            50% { stroke-dashoffset: 0; opacity: 1; }
            100% { stroke-dashoffset: -20; opacity: 0.6; }
          }
        `}
      </style>
      <path
        className="share-node share-node-1"
        d="M7.49992 9.99935C7.49992 11.1499 6.56718 12.0827 5.41659 12.0827C4.26599 12.0827 3.33325 11.1499 3.33325 9.99935C3.33325 8.84876 4.26599 7.91602 5.41659 7.91602C6.56718 7.91602 7.49992 8.84876 7.49992 9.99935Z"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
      />
      <path
        className="share-connection"
        d="M11.6667 5.41602L7.5 8.33268"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        className="share-connection"
        d="M11.6667 14.584L7.5 11.6673"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        className="share-node share-node-2"
        d="M15.8334 15.4173C15.8334 16.5679 14.9007 17.5007 13.7501 17.5007C12.5995 17.5007 11.6667 16.5679 11.6667 15.4173C11.6667 14.2667 12.5995 13.334 13.7501 13.334C14.9007 13.334 15.8334 14.2667 15.8334 15.4173Z"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
      />
      <path
        className="share-node share-node-3"
        d="M15.8334 4.58333C15.8334 5.73393 14.9007 6.66667 13.7501 6.66667C12.5995 6.66667 11.6667 5.73393 11.6667 4.58333C11.6667 3.43274 12.5995 2.5 13.7501 2.5C14.9007 2.5 15.8334 3.43274 15.8334 4.58333Z"
        stroke={color || "#707FF5"}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export { ShareIcon };

// Animated Plane Icon BankNoteIcon
const BankNoteIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_497_8004)">
<path d="M1.6792 9.66602C1.6792 7.30899 1.6792 6.13048 2.41143 5.39825C3.14367 4.66602 4.32218 4.66602 6.6792 4.66602H10.8459C13.2029 4.66602 14.3814 4.66602 15.1136 5.39825C15.8459 6.13048 15.8459 7.30899 15.8459 9.66602C15.8459 12.023 15.8459 13.2015 15.1136 13.9338C14.3814 14.666 13.2029 14.666 10.8459 14.666H6.6792C4.32218 14.666 3.14367 14.666 2.41143 13.9338C1.6792 13.2015 1.6792 12.023 1.6792 9.66602Z" stroke="#384254" stroke-width="1.5"/>
<path d="M15.8459 7.23047C16.6585 7.3104 17.2054 7.49103 17.6137 7.89927C18.3459 8.6315 18.3459 9.81001 18.3459 12.167C18.3459 14.5241 18.3459 15.7026 17.6137 16.4348C16.8814 17.167 15.7029 17.167 13.3459 17.167H9.17925C6.82222 17.167 5.64371 17.167 4.91148 16.4348C4.50323 16.0266 4.3226 15.4796 4.24268 14.667" stroke="#384254" stroke-width="1.5"/>
<path d="M10.8459 9.66732C10.8459 10.8179 9.91313 11.7507 8.76253 11.7507C7.61194 11.7507 6.6792 10.8179 6.6792 9.66732C6.6792 8.51672 7.61194 7.58398 8.76253 7.58398C9.91313 7.58398 10.8459 8.51672 10.8459 9.66732Z" stroke="#384254" stroke-width="1.5"/>
<path d="M13.3457 11.334L13.3457 8.00065" stroke="#384254" stroke-width="1.5" stroke-linecap="round"/>
<path d="M4.1792 11.334L4.1792 8.00065" stroke="#384254" stroke-width="1.5" stroke-linecap="round"/>
</g>
<defs>
<clipPath id="clip0_497_8004">
<rect width="20" height="20" fill="white" transform="translate(0.0124512 0.5)"/>
</clipPath>
</defs>
</svg>

  );
};

export { BankNoteIcon };

// Animated Plane Icon CalendarIcon
const CalendarIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.9199 11.1673C14.3802 11.1673 14.7533 10.7942 14.7533 10.334C14.7533 9.87375 14.3802 9.50065 13.9199 9.50065C13.4597 9.50065 13.0866 9.87375 13.0866 10.334C13.0866 10.7942 13.4597 11.1673 13.9199 11.1673Z" fill="#384254"/>
<path d="M13.9199 14.5007C14.3802 14.5007 14.7533 14.1276 14.7533 13.6673C14.7533 13.2071 14.3802 12.834 13.9199 12.834C13.4597 12.834 13.0866 13.2071 13.0866 13.6673C13.0866 14.1276 13.4597 14.5007 13.9199 14.5007Z" fill="#384254"/>
<path d="M10.5866 10.334C10.5866 10.7942 10.2135 11.1673 9.75326 11.1673C9.29302 11.1673 8.91992 10.7942 8.91992 10.334C8.91992 9.87375 9.29302 9.50065 9.75326 9.50065C10.2135 9.50065 10.5866 9.87375 10.5866 10.334Z" fill="#384254"/>
<path d="M10.5866 13.6673C10.5866 14.1276 10.2135 14.5007 9.75326 14.5007C9.29302 14.5007 8.91992 14.1276 8.91992 13.6673C8.91992 13.2071 9.29302 12.834 9.75326 12.834C10.2135 12.834 10.5866 13.2071 10.5866 13.6673Z" fill="#384254"/>
<path d="M5.58659 11.1673C6.04683 11.1673 6.41992 10.7942 6.41992 10.334C6.41992 9.87375 6.04683 9.50065 5.58659 9.50065C5.12635 9.50065 4.75326 9.87375 4.75326 10.334C4.75326 10.7942 5.12635 11.1673 5.58659 11.1673Z" fill="#384254"/>
<path d="M5.58659 14.5007C6.04683 14.5007 6.41992 14.1276 6.41992 13.6673C6.41992 13.2071 6.04683 12.834 5.58659 12.834C5.12635 12.834 4.75326 13.2071 4.75326 13.6673C4.75326 14.1276 5.12635 14.5007 5.58659 14.5007Z" fill="#384254"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.58659 0.958984C5.93177 0.958984 6.21159 1.23881 6.21159 1.58398V2.21958C6.76325 2.20897 7.37103 2.20898 8.03947 2.20898H11.4669C12.1354 2.20898 12.7433 2.20897 13.2949 2.21958V1.58398C13.2949 1.23881 13.5747 0.958984 13.9199 0.958984C14.2651 0.958984 14.5449 1.23881 14.5449 1.58398V2.27322C14.7615 2.28974 14.9667 2.3105 15.1608 2.3366C16.1378 2.46795 16.9286 2.74472 17.5522 3.36835C18.1759 3.99199 18.4526 4.78278 18.584 5.7598C18.7116 6.70914 18.7116 7.92215 18.7116 9.4536V11.2143C18.7116 12.7458 18.7116 13.9588 18.584 14.9082C18.4526 15.8852 18.1759 16.676 17.5522 17.2996C16.9286 17.9233 16.1378 18.2 15.1608 18.3314C14.2114 18.459 12.9984 18.459 11.467 18.459H8.03958C6.50813 18.459 5.29507 18.459 4.34574 18.3314C3.36872 18.2 2.57793 17.9233 1.95429 17.2996C1.33066 16.676 1.05389 15.8852 0.922534 14.9082C0.794899 13.9588 0.794909 12.7458 0.794922 11.2143V9.45364C0.794909 7.92217 0.794899 6.70914 0.922534 5.7598C1.05389 4.78278 1.33066 3.99199 1.95429 3.36835C2.57793 2.74472 3.36872 2.46795 4.34574 2.3366C4.53984 2.3105 4.74497 2.28974 4.96159 2.27322V1.58398C4.96159 1.23881 5.24141 0.958984 5.58659 0.958984ZM4.5123 3.57545C3.67389 3.68817 3.19085 3.89956 2.83817 4.25224C2.4855 4.60491 2.27411 5.08795 2.16139 5.92636C2.1423 6.06835 2.12634 6.21783 2.11299 6.37565H17.3935C17.3802 6.21783 17.3642 6.06835 17.3451 5.92636C17.2324 5.08795 17.021 4.60491 16.6683 4.25224C16.3157 3.89956 15.8326 3.68817 14.9942 3.57545C14.1378 3.46031 13.0089 3.45898 11.4199 3.45898H8.08659C6.49757 3.45898 5.36868 3.46031 4.5123 3.57545ZM2.04492 9.50065C2.04492 8.78897 2.04519 8.16959 2.05583 7.62565H17.4507C17.4613 8.16959 17.4616 8.78897 17.4616 9.50065V11.1673C17.4616 12.7563 17.4603 13.8852 17.3451 14.7416C17.2324 15.58 17.021 16.0631 16.6683 16.4157C16.3157 16.7684 15.8326 16.9798 14.9942 17.0925C14.1378 17.2077 13.0089 17.209 11.4199 17.209H8.08659C6.49757 17.209 5.36868 17.2077 4.5123 17.0925C3.67389 16.9798 3.19085 16.7684 2.83817 16.4157C2.4855 16.0631 2.27411 15.58 2.16139 14.7416C2.04625 13.8852 2.04492 12.7563 2.04492 11.1673V9.50065Z" fill="#384254"/>
</svg>

  );
};

export { CalendarIcon };

// Animated Plane Icon LocationIcon
const LocationIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.2501 15.0827C16.2501 15.5429 15.877 15.916 15.4167 15.916C14.9565 15.916 14.5834 15.5429 14.5834 15.0827C14.5834 14.6224 14.9565 14.2494 15.4167 14.2494C15.877 14.2494 16.2501 14.6224 16.2501 15.0827Z" fill="#384254"/>
<path d="M5.41675 5.08268C5.41675 5.54292 5.04365 5.91602 4.58341 5.91602C4.12318 5.91602 3.75008 5.54292 3.75008 5.08268C3.75008 4.62245 4.12318 4.24935 4.58341 4.24935C5.04365 4.24935 5.41675 4.62245 5.41675 5.08268Z" fill="#384254"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.04175 4.88045C1.04175 2.99459 2.67046 1.54102 4.58341 1.54102C6.49637 1.54102 8.12508 2.99459 8.12508 4.88045C8.12508 6.56901 7.08691 8.56192 5.36238 9.29889C4.86693 9.51062 4.2999 9.51062 3.80445 9.29889C2.07992 8.56192 1.04175 6.56901 1.04175 4.88045ZM4.58341 2.79102C3.27471 2.79102 2.29175 3.76802 2.29175 4.88045C2.29175 6.16667 3.11537 7.64506 4.29566 8.14945C4.47736 8.2271 4.68947 8.2271 4.87117 8.14945C6.05146 7.64506 6.87508 6.16667 6.87508 4.88045C6.87508 3.76802 5.89212 2.79102 4.58341 2.79102ZM9.37508 4.66602C9.37508 4.32084 9.6549 4.04102 10.0001 4.04102H13.4433C15.736 4.04102 16.6079 7.03518 14.6737 8.26607L5.9976 13.7872C5.11839 14.3467 5.51472 15.7077 6.55684 15.7077H8.4912L8.30814 15.5246C8.06406 15.2805 8.06406 14.8848 8.30814 14.6407C8.55222 14.3967 8.94795 14.3967 9.19202 14.6407L10.442 15.8907C10.6861 16.1348 10.6861 16.5305 10.442 16.7746L9.19202 18.0246C8.94795 18.2687 8.55222 18.2687 8.30814 18.0246C8.06406 17.7805 8.06406 17.3848 8.30814 17.1407L8.4912 16.9577H6.55684C4.26414 16.9577 3.39226 13.9635 5.32651 12.7326L14.0026 7.2115C14.8818 6.652 14.4855 5.29102 13.4433 5.29102H10.0001C9.6549 5.29102 9.37508 5.01119 9.37508 4.66602ZM11.8751 14.8804C11.8751 12.9946 13.5038 11.541 15.4167 11.541C17.3297 11.541 18.9584 12.9946 18.9584 14.8804C18.9584 16.569 17.9202 18.5619 16.1957 19.2989C15.7003 19.5106 15.1332 19.5106 14.6378 19.2989C12.9133 18.5619 11.8751 16.569 11.8751 14.8804ZM15.4167 12.791C14.108 12.791 13.1251 13.768 13.1251 14.8804C13.1251 16.1667 13.9487 17.6451 15.129 18.1494C15.3107 18.2271 15.5228 18.2271 15.7045 18.1494C16.8848 17.6451 17.7084 16.1667 17.7084 14.8804C17.7084 13.768 16.7255 12.791 15.4167 12.791Z" fill="#384254"/>
</svg>

  );
};

export { LocationIcon };

// Animated Plane Icon PaletteIcon
const PaletteIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.92603 5.49935C1.92603 3.6584 3.41841 2.16602 5.25936 2.16602C7.10031 2.16602 8.59269 3.6584 8.59269 5.49935V15.4993C8.59269 17.3403 7.10031 18.8327 5.25936 18.8327C3.41841 18.8327 1.92603 17.3403 1.92603 15.4993V5.49935Z" stroke="#384254" stroke-width="1.5"/>
<path d="M8.59266 7.36856L11.354 4.60717C12.6558 3.30542 14.7663 3.30542 16.0681 4.60717C17.3698 5.90892 17.3698 8.01947 16.0681 9.32122L8.01465 17.3747" stroke="#384254" stroke-width="1.5"/>
<path d="M5.25928 18.8327L15.2593 18.8327C17.1002 18.8327 18.5926 17.3403 18.5926 15.4993C18.5926 13.6584 17.1002 12.166 15.2593 12.166L13.1759 12.166" stroke="#384254" stroke-width="1.5"/>
<path d="M6.09269 15.4993C6.09269 15.9596 5.7196 16.3327 5.25936 16.3327C4.79912 16.3327 4.42603 15.9596 4.42603 15.4993C4.42603 15.0391 4.79912 14.666 5.25936 14.666C5.7196 14.666 6.09269 15.0391 6.09269 15.4993Z" stroke="#384254" stroke-width="1.5"/>
</svg>
  );
};

export { PaletteIcon };

// Animated Plane Icon UserGroupIcon
const UserGroupIcon: React.FC<AnimatedIconProps> = ({
  size = 24,
  className = '',
  color = 'currentColor',
}) => {
  return (
<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_497_7987)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5063 1.54102C8.32014 1.54102 6.54793 3.31322 6.54793 5.49935C6.54793 7.68548 8.32014 9.45768 10.5063 9.45768C12.6924 9.45768 14.4646 7.68548 14.4646 5.49935C14.4646 3.31322 12.6924 1.54102 10.5063 1.54102ZM7.79793 5.49935C7.79793 4.00358 9.01049 2.79102 10.5063 2.79102C12.002 2.79102 13.2146 4.00358 13.2146 5.49935C13.2146 6.99512 12.002 8.20768 10.5063 8.20768C9.01049 8.20768 7.79793 6.99512 7.79793 5.49935Z" fill="#384254"/>
<path d="M15.5063 3.20768C15.1611 3.20768 14.8813 3.4875 14.8813 3.83268C14.8813 4.17786 15.1611 4.45768 15.5063 4.45768C16.6533 4.45768 17.3813 5.21246 17.3813 5.91602C17.3813 6.61957 16.6533 7.37435 15.5063 7.37435C15.1611 7.37435 14.8813 7.65417 14.8813 7.99935C14.8813 8.34453 15.1611 8.62435 15.5063 8.62435C17.1206 8.62435 18.6313 7.51364 18.6313 5.91602C18.6313 4.31839 17.1206 3.20768 15.5063 3.20768Z" fill="#384254"/>
<path d="M6.13127 3.83268C6.13127 3.4875 5.85144 3.20768 5.50627 3.20768C3.89191 3.20768 2.38127 4.31839 2.38127 5.91602C2.38127 7.51364 3.89191 8.62435 5.50627 8.62435C5.85144 8.62435 6.13127 8.34453 6.13127 7.99935C6.13127 7.65417 5.85144 7.37435 5.50627 7.37435C4.35919 7.37435 3.63127 6.61957 3.63127 5.91602C3.63127 5.21246 4.35919 4.45768 5.50627 4.45768C5.85144 4.45768 6.13127 4.17786 6.13127 3.83268Z" fill="#384254"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5063 10.7077C9.01937 10.7077 7.64497 11.1083 6.62404 11.789C5.60748 12.4667 4.88127 13.4716 4.88127 14.666C4.88127 15.8605 5.60748 16.8654 6.62404 17.5431C7.64497 18.2237 9.01937 18.6243 10.5063 18.6243C11.9932 18.6243 13.3676 18.2237 14.3885 17.5431C15.405 16.8654 16.1313 15.8605 16.1313 14.666C16.1313 13.4716 15.405 12.4667 14.3885 11.789C13.3676 11.1083 11.9932 10.7077 10.5063 10.7077ZM6.13127 14.666C6.13127 14.0195 6.52434 13.3577 7.31742 12.829C8.10613 12.3032 9.23173 11.9577 10.5063 11.9577C11.7808 11.9577 12.9064 12.3032 13.6951 12.829C14.4882 13.3577 14.8813 14.0195 14.8813 14.666C14.8813 15.3125 14.4882 15.9743 13.6951 16.503C12.9064 17.0288 11.7808 17.3743 10.5063 17.3743C9.23173 17.3743 8.10613 17.0288 7.31742 16.503C6.52434 15.9743 6.13127 15.3125 6.13127 14.666Z" fill="#384254"/>
<path d="M16.5624 12.0321C16.6364 11.695 16.9696 11.4816 17.3068 11.5555C18.1084 11.7313 18.8307 12.0488 19.3669 12.4876C19.9027 12.926 20.2979 13.5287 20.2979 14.2493C20.2979 14.97 19.9027 15.5727 19.3669 16.0111C18.8307 16.4499 18.1084 16.7674 17.3068 16.9432C16.9696 17.0171 16.6364 16.8037 16.5624 16.4666C16.4885 16.1294 16.7019 15.7961 17.0391 15.7222C17.6994 15.5774 18.2271 15.3287 18.5753 15.0437C18.9241 14.7583 19.0479 14.4796 19.0479 14.2493C19.0479 14.0191 18.9241 13.7404 18.5753 13.455C18.2271 13.17 17.6994 12.9213 17.0391 12.7765C16.7019 12.7026 16.4885 12.3693 16.5624 12.0321Z" fill="#384254"/>
<path d="M3.70572 11.5555C4.04289 11.4816 4.37615 11.695 4.45009 12.0321C4.52403 12.3693 4.31064 12.7026 3.97348 12.7765C3.31317 12.9213 2.78548 13.17 2.4372 13.455C2.08846 13.7404 1.9646 14.0191 1.9646 14.2493C1.9646 14.4796 2.08846 14.7583 2.4372 15.0437C2.78548 15.3287 3.31317 15.5774 3.97348 15.7222C4.31064 15.7961 4.52403 16.1294 4.45009 16.4666C4.37615 16.8037 4.04289 17.0171 3.70572 16.9432C2.90415 16.7674 2.18185 16.4499 1.64559 16.0111C1.10981 15.5727 0.7146 14.97 0.7146 14.2493C0.7146 13.5287 1.10981 12.926 1.64559 12.4876C2.18185 12.0488 2.90415 11.7313 3.70572 11.5555Z" fill="#384254"/>
</g>
<defs>
<clipPath id="clip0_497_7987">
<rect width="20" height="20" fill="white" transform="translate(0.506348 0.5)"/>
</clipPath>
</defs>
</svg>

  );
};

export { UserGroupIcon };
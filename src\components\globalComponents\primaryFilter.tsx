import { SlLocationPin } from 'react-icons/sl';

const PrimaryFilter = () => {
  return (
    <div className="grid grid-cols-5 grid-flow-row w-full bg-white rounded-xl p-4">
      <div>
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          LOCATION
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Where to go</p>
        </div>
      </div>
      <div className=" border-l-2 pl-4">
        {' '}
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          NUMBER OF DAY
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">When is it</p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        {' '}
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          TOTAL TRAVELERS
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Solo or Group</p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        {' '}
        <p className=" uppercase text-[#1C1C1C] text-base font-medium opacity-50">
          TRAVEL STYLE
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">
            What’s your style
          </p>
        </div>
      </div>

      <div className=" border-l-2 pl-4">
        <p className="text-[#1C1C1C] text-base font-medium opacity-50 uppercase">
          BUDGET
        </p>
        <div className="flex items-center space-x-2 mt-1.5">
          <SlLocationPin size={20} />
          <p className="text-[#1C1C1C] text-sm font-medium">Total estimate</p>
        </div>
      </div>
    </div>
  );
};

export default PrimaryFilter;
